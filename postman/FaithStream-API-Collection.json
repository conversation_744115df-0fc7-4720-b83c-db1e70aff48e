{"info": {"name": "FaithStream VOD Platform API", "description": "Complete API collection for FaithStream VOD Platform with authentication, content management, streaming, and analytics", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3090/api/v1", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "videoId", "value": "", "type": "string"}, {"key": "categoryId", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"username\": \"johndo<PERSON>\",\n  \"phoneNumber\": \"+**********\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"username\": \"johndo<PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"user\": {\n    \"id\": \"7f91364d-3803-4e3a-ae70-f3ddcba25b0f\",\n    \"email\": \"<EMAIL>\",\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"username\": \"johndo<PERSON>\",\n    \"phoneNumber\": \"+**********\",\n    \"status\": \"active\",\n    \"authProvider\": \"local\",\n    \"emailVerified\": false,\n    \"twoFactorEnabled\": false,\n    \"createdAt\": \"2025-06-06T00:06:15.690Z\",\n    \"updatedAt\": \"2025-06-06T00:06:15.690Z\",\n    \"roles\": [\n      {\n        \"id\": \"f2d1d054-5318-4a37-aa77-ee5e0364a3c9\",\n        \"name\": \"standard_user\",\n        \"description\": \"Regular platform user\",\n        \"type\": \"standard_user\"\n      }\n    ]\n  },\n  \"accessToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n}"}, {"name": "Register Content Creator", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"CreatorPass123!\",\n  \"firstName\": \"Content\",\n  \"lastName\": \"Creator\",\n  \"username\": \"contentcreator\",\n  \"phoneNumber\": \"+**********\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/content-creator", "host": ["{{baseUrl}}"], "path": ["auth", "register", "content-creator"]}}, "response": []}, {"name": "Register Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"AdminPass123!\",\n  \"firstName\": \"Admin\",\n  \"lastName\": \"User\",\n  \"username\": \"adminuser\",\n  \"phoneNumber\": \"+1234567892\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/admin", "host": ["{{baseUrl}}"], "path": ["auth", "register", "admin"]}}, "response": []}]}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('accessToken', response.accessToken);", "    pm.collectionVariables.set('userId', response.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin123!\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin123!\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"user\": {\n    \"id\": \"985de753-16fd-4bc7-849f-f094e9401ed9\",\n    \"email\": \"<EMAIL>\",\n    \"firstName\": \"Super\",\n    \"lastName\": \"Admin\",\n    \"username\": \"superadmin\",\n    \"status\": \"active\",\n    \"emailVerified\": true,\n    \"lastLogin\": \"2025-06-06T00:07:33.266Z\",\n    \"profile\": {\n      \"id\": \"4df8f9b4-1c85-4a57-8332-9b4e0fba07fb\",\n      \"theme\": \"auto\",\n      \"language\": \"en\",\n      \"watchTimeMinutes\": 0,\n      \"videosWatched\": 0\n    },\n    \"roles\": [\n      {\n        \"id\": \"c9384bf1-8ce4-4d93-8fdd-14d7ecc3ef49\",\n        \"name\": \"super_admin\",\n        \"description\": \"Full system access\",\n        \"type\": \"super_admin\"\n      }\n    ]\n  },\n  \"accessToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n}"}]}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/profile", "host": ["{{baseUrl}}"], "path": ["auth", "profile"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"id\": \"985de753-16fd-4bc7-849f-f094e9401ed9\",\n  \"email\": \"<EMAIL>\",\n  \"firstName\": \"Super\",\n  \"lastName\": \"Admin\",\n  \"username\": \"superadmin\",\n  \"status\": \"active\",\n  \"emailVerified\": true,\n  \"profile\": {\n    \"theme\": \"auto\",\n    \"language\": \"en\",\n    \"watchTimeMinutes\": 0,\n    \"videosWatched\": 0\n  },\n  \"roles\": [\"super_admin\"]\n}"}]}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/forgot-password", "host": ["{{baseUrl}}"], "path": ["auth", "forgot-password"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"message\": \"Password reset email sent successfully\"\n}"}]}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset-token-here\",\n  \"newPassword\": \"NewSecurePass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["auth", "reset-password"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"message\": \"Password reset successfully\"\n}"}]}]}, {"name": "Users", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users?page=1&limit=10&search=john&status=active", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": "john"}, {"key": "status", "value": "active"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"data\": [\n    {\n      \"id\": \"7f91364d-3803-4e3a-ae70-f3ddcba25b0f\",\n      \"email\": \"<EMAIL>\",\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"<PERSON><PERSON>\",\n      \"username\": \"johndoe\",\n      \"status\": \"active\",\n      \"createdAt\": \"2025-06-06T00:06:15.690Z\",\n      \"roles\": [\"standard_user\"]\n    }\n  ],\n  \"total\": 1,\n  \"page\": 1,\n  \"limit\": 10,\n  \"totalPages\": 1\n}"}]}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users/me/stats", "host": ["{{baseUrl}}"], "path": ["users", "me", "stats"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"watchTimeMinutes\": 1250,\n  \"videosWatched\": 45,\n  \"favoriteGenres\": [\"Faith-Based\", \"Inspirational\"],\n  \"rewardPoints\": 150,\n  \"referralCount\": 3,\n  \"joinedDate\": \"2025-06-06T00:06:15.690Z\"\n}"}]}]}, {"name": "Content Management", "item": [{"name": "Get All Videos", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/content?page=1&limit=10&status=published&category=faith-based&type=movie", "host": ["{{baseUrl}}"], "path": ["content"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "published"}, {"key": "category", "value": "faith-based"}, {"key": "type", "value": "movie"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"data\": [\n    {\n      \"id\": \"video-uuid-here\",\n      \"title\": \"Faith Journey\",\n      \"description\": \"An inspiring story of faith and perseverance\",\n      \"thumbnail\": \"https://example.com/thumbnail.jpg\",\n      \"videoUrl\": \"https://example.com/video.mp4\",\n      \"duration\": 7200,\n      \"status\": \"published\",\n      \"type\": \"movie\",\n      \"ageRating\": \"PG\",\n      \"language\": \"en\",\n      \"viewCount\": 1250,\n      \"likeCount\": 89,\n      \"averageRating\": 4.5,\n      \"releaseDate\": \"2025-01-15T00:00:00.000Z\",\n      \"categories\": [\"Faith-Based\", \"Inspirational\"],\n      \"tags\": [\"faith\", \"inspiration\", \"family\"],\n      \"uploader\": {\n        \"id\": \"uploader-uuid\",\n        \"firstName\": \"Content\",\n        \"lastName\": \"Creator\"\n      }\n    }\n  ],\n  \"total\": 1,\n  \"page\": 1,\n  \"limit\": 10\n}"}]}, {"name": "Get Featured Videos", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/content/featured?limit=5", "host": ["{{baseUrl}}"], "path": ["content", "featured"], "query": [{"key": "limit", "value": "5"}]}}}, {"name": "Get Trending Videos", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/content/trending?limit=10&timeframe=week", "host": ["{{baseUrl}}"], "path": ["content", "trending"], "query": [{"key": "limit", "value": "10"}, {"key": "timeframe", "value": "week"}]}}}, {"name": "Get Recommended Videos", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/content/recommended?limit=10", "host": ["{{baseUrl}}"], "path": ["content", "recommended"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Search Videos", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/content/search?q=faith&category=inspirational&language=en&sort=relevance", "host": ["{{baseUrl}}"], "path": ["content", "search"], "query": [{"key": "q", "value": "faith"}, {"key": "category", "value": "inspirational"}, {"key": "language", "value": "en"}, {"key": "sort", "value": "relevance"}]}}}, {"name": "Get Video by ID", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('videoId', response.id);", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/content/{{videoId}}", "host": ["{{baseUrl}}"], "path": ["content", "{{videoId}}"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"id\": \"video-uuid-here\",\n  \"title\": \"Faith Journey\",\n  \"description\": \"An inspiring story of faith and perseverance that follows...\",\n  \"thumbnail\": \"https://example.com/thumbnail.jpg\",\n  \"videoUrl\": \"https://example.com/video.mp4\",\n  \"trailerUrl\": \"https://example.com/trailer.mp4\",\n  \"duration\": 7200,\n  \"status\": \"published\",\n  \"type\": \"movie\",\n  \"ageRating\": \"PG\",\n  \"language\": \"en\",\n  \"subtitleLanguages\": [\"en\", \"es\", \"fr\"],\n  \"viewCount\": 1250,\n  \"likeCount\": 89,\n  \"commentCount\": 23,\n  \"shareCount\": 15,\n  \"averageRating\": 4.5,\n  \"ratingCount\": 67,\n  \"releaseDate\": \"2025-01-15T00:00:00.000Z\",\n  \"isFeatured\": true,\n  \"isTrending\": false,\n  \"allowComments\": true,\n  \"allowLikes\": true,\n  \"allowSharing\": true,\n  \"keywords\": [\"faith\", \"inspiration\", \"family\", \"christian\"],\n  \"metadata\": {\n    \"director\": \"<PERSON>\",\n    \"producer\": \"Faith Films\",\n    \"cast\": [\"Actor 1\", \"Actor 2\"]\n  },\n  \"categories\": [\n    {\n      \"id\": \"category-uuid\",\n      \"name\": \"Faith-Based\",\n      \"slug\": \"faith-based\",\n      \"type\": \"genre\"\n    }\n  ],\n  \"tags\": [\n    {\n      \"id\": \"tag-uuid\",\n      \"name\": \"faith\",\n      \"slug\": \"faith\"\n    }\n  ],\n  \"uploader\": {\n    \"id\": \"uploader-uuid\",\n    \"firstName\": \"Content\",\n    \"lastName\": \"Creator\",\n    \"username\": \"contentcreator\"\n  },\n  \"streams\": [\n    {\n      \"id\": \"stream-uuid\",\n      \"quality\": \"1080p\",\n      \"format\": \"hls\",\n      \"streamUrl\": \"https://example.com/stream/1080p.m3u8\",\n      \"fileSizeBytes\": 2147483648,\n      \"bitrateKbps\": 5000\n    }\n  ]\n}"}]}, {"name": "Create Video", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"New Faith Documentary\",\n  \"description\": \"A powerful documentary about faith in modern times\",\n  \"videoUrl\": \"https://example.com/new-video.mp4\",\n  \"thumbnail\": \"https://example.com/new-thumbnail.jpg\",\n  \"trailerUrl\": \"https://example.com/new-trailer.mp4\",\n  \"duration\": 5400,\n  \"type\": \"documentary\",\n  \"ageRating\": \"PG\",\n  \"language\": \"en\",\n  \"subtitleLanguages\": [\"en\", \"es\"],\n  \"releaseDate\": \"2025-02-01T00:00:00.000Z\",\n  \"allowComments\": true,\n  \"allowLikes\": true,\n  \"allowSharing\": true,\n  \"keywords\": [\"faith\", \"documentary\", \"modern\", \"christian\"],\n  \"metadata\": {\n    \"director\": \"<PERSON>\",\n    \"producer\": \"Faith Productions\"\n  },\n  \"categoryIds\": [\"category-uuid-1\", \"category-uuid-2\"],\n  \"tagIds\": [\"tag-uuid-1\", \"tag-uuid-2\"]\n}"}, "url": {"raw": "{{baseUrl}}/content", "host": ["{{baseUrl}}"], "path": ["content"]}}, "response": [{"name": "Success Response", "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"id\": \"new-video-uuid\",\n  \"title\": \"New Faith Documentary\",\n  \"description\": \"A powerful documentary about faith in modern times\",\n  \"status\": \"draft\",\n  \"type\": \"documentary\",\n  \"createdAt\": \"2025-06-06T00:15:00.000Z\",\n  \"message\": \"Video created successfully\"\n}"}]}]}, {"name": "Categories", "item": [{"name": "Get All Categories", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.length > 0) {", "        pm.collectionVariables.set('categoryId', response[0].id);", "    }", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories?type=genre&active=true", "host": ["{{baseUrl}}"], "path": ["categories"], "query": [{"key": "type", "value": "genre"}, {"key": "active", "value": "true"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "[\n  {\n    \"id\": \"261d9c43-b77c-413d-a953-188dcc186d10\",\n    \"name\": \"Faith-Based\",\n    \"description\": null,\n    \"slug\": \"faith-based\",\n    \"type\": \"genre\",\n    \"icon\": \"fa-cross\",\n    \"color\": \"#4A90E2\",\n    \"bannerImage\": null,\n    \"active\": true,\n    \"sortOrder\": 0,\n    \"parentId\": null,\n    \"createdAt\": \"2025-06-05T23:45:27.101Z\",\n    \"updatedAt\": \"2025-06-05T23:45:27.101Z\",\n    \"parent\": null,\n    \"children\": []\n  },\n  {\n    \"id\": \"5930a85c-7c66-4f03-80dc-e6ef6650ee22\",\n    \"name\": \"Inspirational\",\n    \"description\": null,\n    \"slug\": \"inspirational\",\n    \"type\": \"genre\",\n    \"icon\": \"fa-heart\",\n    \"color\": \"#F5A623\",\n    \"bannerImage\": null,\n    \"active\": true,\n    \"sortOrder\": 0,\n    \"parentId\": null,\n    \"createdAt\": \"2025-06-05T23:45:27.105Z\",\n    \"updatedAt\": \"2025-06-05T23:45:27.105Z\",\n    \"parent\": null,\n    \"children\": []\n  }\n]"}]}, {"name": "Get Category Hierarchy", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/hierarchy", "host": ["{{baseUrl}}"], "path": ["categories", "hierarchy"]}}}, {"name": "Get Popular Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/popular?limit=5", "host": ["{{baseUrl}}"], "path": ["categories", "popular"], "query": [{"key": "limit", "value": "5"}]}}}, {"name": "Get Category by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/slug/faith-based", "host": ["{{baseUrl}}"], "path": ["categories", "slug", "faith-based"]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Worship\",\n  \"description\": \"Worship and praise content\",\n  \"type\": \"genre\",\n  \"icon\": \"fa-music\",\n  \"color\": \"#9B59B6\",\n  \"sortOrder\": 10,\n  \"parentId\": null\n}"}, "url": {"raw": "{{baseUrl}}/categories", "host": ["{{baseUrl}}"], "path": ["categories"]}}, "response": [{"name": "Success Response", "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"id\": \"new-category-uuid\",\n  \"name\": \"Worship\",\n  \"description\": \"Worship and praise content\",\n  \"slug\": \"worship\",\n  \"type\": \"genre\",\n  \"icon\": \"fa-music\",\n  \"color\": \"#9B59B6\",\n  \"active\": true,\n  \"sortOrder\": 10,\n  \"createdAt\": \"2025-06-06T00:20:00.000Z\"\n}"}]}]}, {"name": "Tags", "item": [{"name": "Get All Tags", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tags?search=faith&limit=20", "host": ["{{baseUrl}}"], "path": ["tags"], "query": [{"key": "search", "value": "faith"}, {"key": "limit", "value": "20"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "[\n  {\n    \"id\": \"tag-uuid-1\",\n    \"name\": \"faith\",\n    \"slug\": \"faith\",\n    \"description\": \"Content related to faith and belief\",\n    \"usageCount\": 45,\n    \"active\": true,\n    \"createdAt\": \"2025-06-05T23:45:27.150Z\"\n  },\n  {\n    \"id\": \"tag-uuid-2\",\n    \"name\": \"inspiration\",\n    \"slug\": \"inspiration\",\n    \"description\": \"Inspirational and motivational content\",\n    \"usageCount\": 32,\n    \"active\": true,\n    \"createdAt\": \"2025-06-05T23:45:27.152Z\"\n  }\n]"}]}, {"name": "Get Popular Tags", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tags/popular?limit=10", "host": ["{{baseUrl}}"], "path": ["tags", "popular"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Get Tag Cloud", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tags/cloud?limit=50", "host": ["{{baseUrl}}"], "path": ["tags", "cloud"], "query": [{"key": "limit", "value": "50"}]}}}, {"name": "Create Tag", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"prayer\",\n  \"description\": \"Content related to prayer and meditation\"\n}"}, "url": {"raw": "{{baseUrl}}/tags", "host": ["{{baseUrl}}"], "path": ["tags"]}}}]}, {"name": "Streaming", "item": [{"name": "Get Video Streams", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/streaming/videos/{{videoId}}/streams", "host": ["{{baseUrl}}"], "path": ["streaming", "videos", "{{videoId}}", "streams"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "[\n  {\n    \"id\": \"stream-uuid-1\",\n    \"quality\": \"480p\",\n    \"format\": \"hls\",\n    \"streamUrl\": \"https://example.com/stream/480p.m3u8\",\n    \"fileSizeBytes\": 524288000,\n    \"bitrateKbps\": 1500,\n    \"active\": true\n  },\n  {\n    \"id\": \"stream-uuid-2\",\n    \"quality\": \"720p\",\n    \"format\": \"hls\",\n    \"streamUrl\": \"https://example.com/stream/720p.m3u8\",\n    \"fileSizeBytes\": 1073741824,\n    \"bitrateKbps\": 3000,\n    \"active\": true\n  },\n  {\n    \"id\": \"stream-uuid-3\",\n    \"quality\": \"1080p\",\n    \"format\": \"hls\",\n    \"streamUrl\": \"https://example.com/stream/1080p.m3u8\",\n    \"fileSizeBytes\": 2147483648,\n    \"bitrateKbps\": 5000,\n    \"active\": true\n  }\n]"}]}, {"name": "Update Watch Progress", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"watchTimeSeconds\": 1800,\n  \"progressPercentage\": 25,\n  \"lastPositionSeconds\": 1800,\n  \"completed\": false,\n  \"quality\": \"1080p\",\n  \"deviceType\": \"desktop\"\n}"}, "url": {"raw": "{{baseUrl}}/streaming/videos/{{videoId}}/progress", "host": ["{{baseUrl}}"], "path": ["streaming", "videos", "{{videoId}}", "progress"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"message\": \"Watch progress updated successfully\",\n  \"watchHistory\": {\n    \"id\": \"watch-history-uuid\",\n    \"watchTimeSeconds\": 1800,\n    \"progressPercentage\": 25,\n    \"lastPositionSeconds\": 1800,\n    \"completed\": false,\n    \"watchCount\": 1,\n    \"updatedAt\": \"2025-06-06T00:25:00.000Z\"\n  }\n}"}]}, {"name": "Get Watch History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/streaming/watch-history?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["streaming", "watch-history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Continue Watching", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/streaming/continue-watching?limit=10", "host": ["{{baseUrl}}"], "path": ["streaming", "continue-watching"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Get Video Comments", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/streaming/videos/{{videoId}}/comments?page=1&limit=20&sort=newest", "host": ["{{baseUrl}}"], "path": ["streaming", "videos", "{{videoId}}", "comments"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "sort", "value": "newest"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"data\": [\n    {\n      \"id\": \"comment-uuid-1\",\n      \"content\": \"This video really touched my heart. Thank you for sharing!\",\n      \"status\": \"active\",\n      \"likeCount\": 5,\n      \"replyCount\": 2,\n      \"isPinned\": false,\n      \"createdAt\": \"2025-06-06T00:20:00.000Z\",\n      \"user\": {\n        \"id\": \"user-uuid\",\n        \"firstName\": \"<PERSON>\",\n        \"lastName\": \"<PERSON><PERSON>\",\n        \"username\": \"johndoe\"\n      },\n      \"replies\": [\n        {\n          \"id\": \"reply-uuid-1\",\n          \"content\": \"I agree! Very inspiring.\",\n          \"createdAt\": \"2025-06-06T00:22:00.000Z\",\n          \"user\": {\n            \"firstName\": \"<PERSON>\",\n            \"lastName\": \"<PERSON>\"\n          }\n        }\n      ]\n    }\n  ],\n  \"total\": 1,\n  \"page\": 1,\n  \"limit\": 20\n}"}]}, {"name": "Post Comment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"Amazing video! This really strengthened my faith.\",\n  \"parentId\": null\n}"}, "url": {"raw": "{{baseUrl}}/streaming/videos/{{videoId}}/comments", "host": ["{{baseUrl}}"], "path": ["streaming", "videos", "{{videoId}}", "comments"]}}}, {"name": "Like Video", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/streaming/videos/{{videoId}}/like", "host": ["{{baseUrl}}"], "path": ["streaming", "videos", "{{videoId}}", "like"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"message\": \"Video liked successfully\",\n  \"likeCount\": 90,\n  \"userLiked\": true\n}"}]}, {"name": "Dislike Video", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/streaming/videos/{{videoId}}/dislike", "host": ["{{baseUrl}}"], "path": ["streaming", "videos", "{{videoId}}", "dislike"]}}}, {"name": "Share Video", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"platform\": \"facebook\",\n  \"message\": \"Check out this amazing faith-based video!\"\n}"}, "url": {"raw": "{{baseUrl}}/streaming/videos/{{videoId}}/share", "host": ["{{baseUrl}}"], "path": ["streaming", "videos", "{{videoId}}", "share"]}}}]}, {"name": "Analytics", "item": [{"name": "Get Analytics Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/analytics?startDate=2025-01-01&endDate=2025-06-06&metrics=views,likes,comments", "host": ["{{baseUrl}}"], "path": ["analytics"], "query": [{"key": "startDate", "value": "2025-01-01"}, {"key": "endDate", "value": "2025-06-06"}, {"key": "metrics", "value": "views,likes,comments"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"overview\": {\n    \"totalViews\": 125000,\n    \"totalLikes\": 8900,\n    \"totalComments\": 2300,\n    \"totalShares\": 1200,\n    \"totalWatchTime\": 450000,\n    \"averageWatchTime\": 3600,\n    \"topCategories\": [\"Faith-Based\", \"Inspirational\", \"Family\"],\n    \"topDevices\": [\"mobile\", \"desktop\", \"tablet\"]\n  },\n  \"timeSeriesData\": [\n    {\n      \"date\": \"2025-06-01\",\n      \"views\": 2500,\n      \"likes\": 180,\n      \"comments\": 45\n    },\n    {\n      \"date\": \"2025-06-02\",\n      \"views\": 2800,\n      \"likes\": 195,\n      \"comments\": 52\n    }\n  ],\n  \"topVideos\": [\n    {\n      \"id\": \"video-uuid-1\",\n      \"title\": \"Faith Journey\",\n      \"views\": 15000,\n      \"likes\": 890,\n      \"comments\": 123\n    }\n  ]\n}"}]}]}, {"name": "Admin", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/dashboard", "host": ["{{baseUrl}}"], "path": ["admin", "dashboard"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"totalUsers\": 15420,\n  \"totalVideos\": 1250,\n  \"totalViews\": 2500000,\n  \"totalRevenue\": 125000.50,\n  \"activeUsers\": 8900,\n  \"pendingVideos\": 23,\n  \"flaggedComments\": 5,\n  \"recentActivity\": [\n    {\n      \"type\": \"user_registration\",\n      \"count\": 45,\n      \"date\": \"2025-06-06\"\n    },\n    {\n      \"type\": \"video_upload\",\n      \"count\": 12,\n      \"date\": \"2025-06-06\"\n    }\n  ],\n  \"topCategories\": [\n    {\n      \"name\": \"Faith-Based\",\n      \"videoCount\": 450,\n      \"viewCount\": 850000\n    }\n  ],\n  \"systemHealth\": {\n    \"status\": \"healthy\",\n    \"uptime\": \"99.9%\",\n    \"lastBackup\": \"2025-06-06T00:00:00.000Z\"\n  }\n}"}]}]}, {"name": "Revenue & Donations", "item": [{"name": "Get Revenue Overview", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/revenue?period=month&year=2025&month=6", "host": ["{{baseUrl}}"], "path": ["revenue"], "query": [{"key": "period", "value": "month"}, {"key": "year", "value": "2025"}, {"key": "month", "value": "6"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"totalRevenue\": 25000.75,\n  \"adRevenue\": 18000.50,\n  \"donationRevenue\": 7000.25,\n  \"subscriptionRevenue\": 0,\n  \"platformFees\": 1250.04,\n  \"netRevenue\": 23750.71,\n  \"transactions\": [\n    {\n      \"id\": \"txn-uuid-1\",\n      \"type\": \"donation\",\n      \"amount\": 50.00,\n      \"currency\": \"USD\",\n      \"status\": \"completed\",\n      \"createdAt\": \"2025-06-06T00:30:00.000Z\",\n      \"donor\": {\n        \"firstName\": \"<PERSON>\",\n        \"lastName\": \"Doe\"\n      },\n      \"recipient\": {\n        \"firstName\": \"Content\",\n        \"lastName\": \"Creator\"\n      }\n    }\n  ],\n  \"topDonors\": [\n    {\n      \"userId\": \"user-uuid-1\",\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"<PERSON>\",\n      \"totalDonated\": 500.00,\n      \"donationCount\": 10\n    }\n  ]\n}"}]}]}]}