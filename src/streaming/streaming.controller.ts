import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { StreamingService } from './streaming.service';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateWatchProgressDto } from './dto/update-watch-progress.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { User } from '../entities/user.entity';
import { LikeType } from '../entities/like.entity';

@ApiTags('Streaming')
@Controller('streaming')
export class StreamingController {
  constructor(private readonly streamingService: StreamingService) {}

  @Public()
  @Get('videos/:id/streams')
  @ApiOperation({ summary: 'Get video stream URLs' })
  @ApiResponse({ status: 200, description: 'Video stream information' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  async getVideoStreams(@Param('id', ParseUUIDPipe) videoId: string) {
    return this.streamingService.getVideoStreams(videoId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('videos/:id/progress')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update watch progress' })
  @ApiResponse({ status: 200, description: 'Watch progress updated successfully' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  async updateWatchProgress(
    @Param('id', ParseUUIDPipe) videoId: string,
    @Body() updateWatchProgressDto: UpdateWatchProgressDto,
    @CurrentUser() user: User,
  ) {
    return this.streamingService.updateWatchProgress(
      user.id,
      videoId,
      updateWatchProgressDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('watch-history')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user watch history' })
  @ApiResponse({ status: 200, description: 'User watch history' })
  async getWatchHistory(
    @CurrentUser() user: User,
    @Query('videoId') videoId?: string,
  ) {
    return this.streamingService.getWatchHistory(user.id, videoId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('continue-watching')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get continue watching list' })
  @ApiResponse({ status: 200, description: 'Continue watching videos' })
  async getContinueWatching(
    @CurrentUser() user: User,
    @Query('limit') limit?: number,
  ) {
    return this.streamingService.getContinueWatching(user.id, limit);
  }

  @Public()
  @Get('videos/:id/comments')
  @ApiOperation({ summary: 'Get video comments' })
  @ApiResponse({ status: 200, description: 'Video comments' })
  async getComments(
    @Param('id', ParseUUIDPipe) videoId: string,
    @Query('parentId') parentId?: string,
  ) {
    return this.streamingService.getComments(videoId, parentId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('videos/:id/comments')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Add comment to video' })
  @ApiResponse({ status: 201, description: 'Comment added successfully' })
  @ApiResponse({ status: 400, description: 'Comments not allowed on this video' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  async addComment(
    @Param('id', ParseUUIDPipe) videoId: string,
    @Body() createCommentDto: CreateCommentDto,
    @CurrentUser() user: User,
  ) {
    return this.streamingService.addComment(user.id, videoId, createCommentDto);
  }

  @UseGuards(JwtAuthGuard)
  @Post('videos/:id/like')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Like video' })
  @ApiResponse({ status: 200, description: 'Video liked successfully' })
  @ApiResponse({ status: 400, description: 'Likes not allowed on this video' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  async likeVideo(
    @Param('id', ParseUUIDPipe) videoId: string,
    @CurrentUser() user: User,
  ) {
    return this.streamingService.toggleLike(user.id, videoId, LikeType.LIKE);
  }

  @UseGuards(JwtAuthGuard)
  @Post('videos/:id/dislike')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Dislike video' })
  @ApiResponse({ status: 200, description: 'Video disliked successfully' })
  @ApiResponse({ status: 400, description: 'Likes not allowed on this video' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  async dislikeVideo(
    @Param('id', ParseUUIDPipe) videoId: string,
    @CurrentUser() user: User,
  ) {
    return this.streamingService.toggleLike(user.id, videoId, LikeType.DISLIKE);
  }

  @UseGuards(JwtAuthGuard)
  @Post('videos/:id/share')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Share video' })
  @ApiResponse({ status: 200, description: 'Video shared successfully' })
  @ApiResponse({ status: 400, description: 'Sharing not allowed for this video' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  async shareVideo(
    @Param('id', ParseUUIDPipe) videoId: string,
    @CurrentUser() user: User,
  ) {
    return this.streamingService.shareVideo(user.id, videoId);
  }
}
