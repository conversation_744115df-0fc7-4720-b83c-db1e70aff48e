import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Video } from '../entities/video.entity';
import { VideoStream } from '../entities/video-stream.entity';
import { WatchHistory } from '../entities/watch-history.entity';
import { Comment } from '../entities/comment.entity';
import { Like, LikeType } from '../entities/like.entity';
import { UserAnalytics, UserEvent } from '../entities/user-analytics.entity';
import { ContentAnalytics, ContentEvent } from '../entities/content-analytics.entity';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateWatchProgressDto } from './dto/update-watch-progress.dto';

@Injectable()
export class StreamingService {
  constructor(
    @InjectRepository(Video)
    private videoRepository: Repository<Video>,
    @InjectRepository(VideoStream)
    private videoStreamRepository: Repository<VideoStream>,
    @InjectRepository(WatchHistory)
    private watchHistoryRepository: Repository<WatchHistory>,
    @InjectRepository(Comment)
    private commentRepository: Repository<Comment>,
    @InjectRepository(Like)
    private likeRepository: Repository<Like>,
    @InjectRepository(UserAnalytics)
    private userAnalyticsRepository: Repository<UserAnalytics>,
    @InjectRepository(ContentAnalytics)
    private contentAnalyticsRepository: Repository<ContentAnalytics>,
  ) {}

  async getVideoStreams(videoId: string) {
    const video = await this.videoRepository.findOne({
      where: { id: videoId },
      relations: ['streams'],
    });

    if (!video) {
      throw new NotFoundException('Video not found');
    }

    return video.streams.filter(stream => stream.active);
  }

  async updateWatchProgress(
    userId: string,
    videoId: string,
    updateWatchProgressDto: UpdateWatchProgressDto,
  ) {
    const { watchTimeSeconds, progressPercentage, completed, lastPositionSeconds } = updateWatchProgressDto;

    let watchHistory = await this.watchHistoryRepository.findOne({
      where: { userId, videoId },
    });

    if (!watchHistory) {
      watchHistory = this.watchHistoryRepository.create({
        userId,
        videoId,
        watchTimeSeconds,
        progressPercentage,
        completed,
        lastPositionSeconds,
      });
    } else {
      watchHistory.watchTimeSeconds = Math.max(watchHistory.watchTimeSeconds, watchTimeSeconds);
      watchHistory.progressPercentage = Math.max(watchHistory.progressPercentage, progressPercentage);
      watchHistory.completed = completed || watchHistory.completed;
      if (lastPositionSeconds !== undefined) {
        watchHistory.lastPositionSeconds = lastPositionSeconds;
      }
      watchHistory.watchCount += 1;
    }

    await this.watchHistoryRepository.save(watchHistory);

    // Track analytics
    await this.trackUserAnalytics(userId, UserEvent.VIDEO_VIEW, videoId);
    await this.trackContentAnalytics(videoId, ContentEvent.WATCH_TIME, watchTimeSeconds, userId);

    if (completed) {
      await this.trackContentAnalytics(videoId, ContentEvent.COMPLETION, 1, userId);
    }

    return { message: 'Watch progress updated successfully' };
  }

  async getWatchHistory(userId: string, videoId?: string) {
    const whereCondition: any = { userId };
    if (videoId) {
      whereCondition.videoId = videoId;
    }

    return this.watchHistoryRepository.find({
      where: whereCondition,
      relations: ['video', 'video.uploader'],
      order: { updatedAt: 'DESC' },
    });
  }

  async addComment(userId: string, videoId: string, createCommentDto: CreateCommentDto) {
    const video = await this.videoRepository.findOne({
      where: { id: videoId },
    });

    if (!video) {
      throw new NotFoundException('Video not found');
    }

    if (!video.allowComments) {
      throw new BadRequestException('Comments are not allowed on this video');
    }

    const comment = this.commentRepository.create({
      ...createCommentDto,
      userId,
      videoId,
    });

    const savedComment = await this.commentRepository.save(comment);

    // Update video comment count
    await this.videoRepository.increment({ id: videoId }, 'commentCount', 1);

    // Track analytics
    await this.trackUserAnalytics(userId, UserEvent.COMMENT_POST, videoId);
    await this.trackContentAnalytics(videoId, ContentEvent.COMMENT, 1, userId);

    return this.commentRepository.findOne({
      where: { id: savedComment.id },
      relations: ['user'],
    });
  }

  async getComments(videoId: string, parentId?: string) {
    const whereCondition: any = { videoId };
    if (parentId) {
      whereCondition.parentId = parentId;
    } else {
      whereCondition.parentId = null;
    }

    return this.commentRepository.find({
      where: whereCondition,
      relations: ['user', 'replies', 'replies.user'],
      order: { createdAt: 'DESC' },
    });
  }

  async toggleLike(userId: string, videoId: string, type: LikeType = LikeType.LIKE) {
    const video = await this.videoRepository.findOne({
      where: { id: videoId },
    });

    if (!video) {
      throw new NotFoundException('Video not found');
    }

    if (!video.allowLikes) {
      throw new BadRequestException('Likes are not allowed on this video');
    }

    let like = await this.likeRepository.findOne({
      where: { userId, videoId },
    });

    let action = 'added';
    let countChange = 1;

    if (like) {
      if (like.type === type) {
        // Remove like/dislike
        await this.likeRepository.remove(like);
        action = 'removed';
        countChange = -1;
      } else {
        // Change like to dislike or vice versa
        like.type = type;
        await this.likeRepository.save(like);
        action = 'changed';
        countChange = 0; // No net change in like count for type change
      }
    } else {
      // Add new like/dislike
      like = this.likeRepository.create({
        userId,
        videoId,
        type,
      });
      await this.likeRepository.save(like);
    }

    // Update video like count (only for likes, not dislikes)
    if (type === LikeType.LIKE && countChange !== 0) {
      await this.videoRepository.increment({ id: videoId }, 'likeCount', countChange);
    }

    // Track analytics
    if (action === 'added') {
      await this.trackUserAnalytics(userId, UserEvent.VIDEO_LIKE, videoId);
      const event = type === LikeType.LIKE ? ContentEvent.LIKE : ContentEvent.DISLIKE;
      await this.trackContentAnalytics(videoId, event, 1, userId);
    }

    return {
      message: `${type} ${action} successfully`,
      action,
      type,
    };
  }

  async shareVideo(userId: string, videoId: string) {
    const video = await this.videoRepository.findOne({
      where: { id: videoId },
    });

    if (!video) {
      throw new NotFoundException('Video not found');
    }

    if (!video.allowSharing) {
      throw new BadRequestException('Sharing is not allowed for this video');
    }

    // Update video share count
    await this.videoRepository.increment({ id: videoId }, 'shareCount', 1);

    // Track analytics
    await this.trackUserAnalytics(userId, UserEvent.VIDEO_SHARE, videoId);
    await this.trackContentAnalytics(videoId, ContentEvent.SHARE, 1, userId);

    return {
      message: 'Video shared successfully',
      shareUrl: `${process.env.FRONTEND_URL}/watch/${videoId}`,
    };
  }

  async getContinueWatching(userId: string, limit: number = 10) {
    return this.watchHistoryRepository.find({
      where: {
        userId,
        progressPercentage: { $gt: 5 } as any, // More than 5% watched
      },
      relations: ['video', 'video.uploader'],
      order: { updatedAt: 'DESC' },
      take: limit,
    });
  }

  private async trackUserAnalytics(userId: string, event: UserEvent, videoId?: string) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let analytics = await this.userAnalyticsRepository.findOne({
      where: {
        userId,
        date: today,
        event,
        videoId: videoId || undefined,
      },
    });

    if (analytics) {
      analytics.count += 1;
    } else {
      analytics = this.userAnalyticsRepository.create({
        userId,
        date: today,
        event,
        count: 1,
        videoId,
      });
    }

    await this.userAnalyticsRepository.save(analytics);
  }

  private async trackContentAnalytics(
    videoId: string,
    event: ContentEvent,
    value: number,
    userId?: string,
  ) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let analytics = await this.contentAnalyticsRepository.findOne({
      where: {
        videoId,
        date: today,
        event,
      },
    });

    if (analytics) {
      analytics.count += 1;
      analytics.totalValue += value;
    } else {
      analytics = this.contentAnalyticsRepository.create({
        videoId,
        date: today,
        event,
        count: 1,
        totalValue: value,
        userId,
      });
    }

    await this.contentAnalyticsRepository.save(analytics);
  }
}
