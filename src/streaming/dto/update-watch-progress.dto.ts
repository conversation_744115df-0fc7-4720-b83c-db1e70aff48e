import { <PERSON><PERSON><PERSON><PERSON>, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateWatchProgressDto {
  @ApiProperty({
    description: 'Total watch time in seconds',
    example: 1800,
  })
  @IsNumber()
  @Min(0)
  watchTimeSeconds: number;

  @ApiProperty({
    description: 'Progress percentage (0-100)',
    example: 75.5,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  progressPercentage: number;

  @ApiProperty({
    description: 'Whether the video was completed',
    example: false,
  })
  @IsBoolean()
  completed: boolean;

  @ApiProperty({
    description: 'Last position in seconds where user stopped watching',
    example: 1800,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  lastPositionSeconds?: number;
}
