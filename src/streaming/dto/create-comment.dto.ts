import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCommentDto {
  @ApiProperty({
    description: 'Comment content',
    example: 'This video really touched my heart and strengthened my faith!',
  })
  @IsString()
  @MinLength(1)
  @MaxLength(1000)
  content: string;

  @ApiProperty({
    description: 'Parent comment ID (for replies)',
    example: 'uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  parentId?: string;
}
