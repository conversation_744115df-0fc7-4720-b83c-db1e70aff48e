import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { StreamingController } from './streaming.controller';
import { StreamingService } from './streaming.service';
import { PlaylistsController } from './playlists.controller';
import { PlaylistsService } from './playlists.service';

import { Video } from '../entities/video.entity';
import { VideoStream } from '../entities/video-stream.entity';
import { WatchHistory } from '../entities/watch-history.entity';
import { Playlist } from '../entities/playlist.entity';
import { PlaylistItem } from '../entities/playlist-item.entity';
import { Comment } from '../entities/comment.entity';
import { Like } from '../entities/like.entity';
import { UserAnalytics } from '../entities/user-analytics.entity';
import { ContentAnalytics } from '../entities/content-analytics.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Video,
      VideoStream,
      WatchHistory,
      Playlist,
      PlaylistItem,
      Comment,
      Like,
      UserAnalytics,
      ContentAnalytics,
    ]),
  ],
  controllers: [StreamingController, PlaylistsController],
  providers: [StreamingService, PlaylistsService],
  exports: [StreamingService, PlaylistsService],
})
export class StreamingModule {}
