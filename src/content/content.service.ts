import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, ILike, In } from 'typeorm';

import { Video, VideoStatus, VideoType } from '../entities/video.entity';
import { Category } from '../entities/category.entity';
import { Tag } from '../entities/tag.entity';
import { VideoStream } from '../entities/video-stream.entity';
import { User } from '../entities/user.entity';
import { CreateVideoDto } from './dto/create-video.dto';
import { UpdateVideoDto } from './dto/update-video.dto';
import { VideoFilterDto } from './dto/video-filter.dto';
import { PaginationDto } from '../common/dto/pagination.dto';
import { ResponseWrapper } from '../common/utils/response-wrapper.util';

@Injectable()
export class ContentService {
  constructor(
    @InjectRepository(Video)
    private videoRepository: Repository<Video>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Tag)
    private tagRepository: Repository<Tag>,
    @InjectRepository(VideoStream)
    private videoStreamRepository: Repository<VideoStream>,
  ) {}

  async create(createVideoDto: CreateVideoDto, uploaderId: string) {
    try {
      const { categoryIds, tagNames, ...videoData } = createVideoDto;

      const video = this.videoRepository.create({
        ...videoData,
        uploaderId,
        status: VideoStatus.DRAFT,
      });

      if (categoryIds && categoryIds.length > 0) {
        const categories = await this.categoryRepository.findBy({
          id: In(categoryIds),
        });
        video.categories = categories;
      }

      if (tagNames && tagNames.length > 0) {
        const tags = await this.findOrCreateTags(tagNames);
        video.tags = tags;
      }

      const savedVideo = await this.videoRepository.save(video);
      const createdVideo = await this.findOne(savedVideo.id);

      return {
        status: true,
        message: 'Video created successfully',
        data: createdVideo,
      };
    } catch (error) {
      console.error(error);
      return {
        status: false,
        message: error.message || 'Failed to create video',
      };
    }
  }

  async findAll(paginationDto: PaginationDto, filterDto?: VideoFilterDto) {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.videoRepository
      .createQueryBuilder('video')
      .leftJoinAndSelect('video.uploader', 'uploader')
      .leftJoinAndSelect('video.categories', 'categories')
      .leftJoinAndSelect('video.tags', 'tags')
      .leftJoinAndSelect('video.streams', 'streams');

    // Apply filters
    if (filterDto?.search) {
      queryBuilder.andWhere(
        '(video.title ILIKE :search OR video.description ILIKE :search)',
        { search: `%${filterDto.search}%` },
      );
    }

    if (filterDto?.status) {
      queryBuilder.andWhere('video.status = :status', { status: filterDto.status });
    }

    if (filterDto?.type) {
      queryBuilder.andWhere('video.type = :type', { type: filterDto.type });
    }

    if (filterDto?.categoryId) {
      queryBuilder.andWhere('categories.id = :categoryId', {
        categoryId: filterDto.categoryId,
      });
    }

    if (filterDto?.language) {
      queryBuilder.andWhere('video.language = :language', {
        language: filterDto.language,
      });
    }

    if (filterDto?.uploaderId) {
      queryBuilder.andWhere('video.uploaderId = :uploaderId', {
        uploaderId: filterDto.uploaderId,
      });
    }

    // Only show published videos for non-admin users
    if (!filterDto?.includeUnpublished) {
      queryBuilder.andWhere('video.status = :publishedStatus', {
        publishedStatus: VideoStatus.PUBLISHED,
      });
    }

    // Apply sorting
    const sortBy = filterDto?.sortBy || 'createdAt';
    const sortOrder = filterDto?.sortOrder || 'DESC';
    queryBuilder.orderBy(`video.${sortBy}`, sortOrder);

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    const [videos, total] = await queryBuilder.getManyAndCount();

    if (total === 0) {
      return ResponseWrapper('No videos found', true, [], 200);
    }
    if (skip >= total) {
      throw new BadRequestException('Page number exceeds total pages');
    }

    const response = {
      data: videos,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };

    return ResponseWrapper(
      'Videos retrieved successfully',
      true,
      response,
      200,
);

    // return {
    
  }

  async findOne(id: string) {
    const video = await this.videoRepository.findOne({
      where: { id },
      relations: [
        'uploader',
        'categories',
        'tags',
        'streams',
        'comments',
        'comments.user',
        'likes',
      ],
    });

    if (!video) {
      throw new NotFoundException('Video not found');
    }

    return video;
  }

  async update(id: string, updateVideoDto: UpdateVideoDto, userId: string) {
    try {
      const video = await this.videoRepository.findOne({
        where: { id },
        relations: ['uploader', 'categories', 'tags'],
      });

      if (!video) {
        throw new NotFoundException('Video not found');
      }

      if (video.uploaderId !== userId) {
        throw new ForbiddenException('You can only update your own videos');
      }

      const { categoryIds, tagNames, ...videoData } = updateVideoDto;

      Object.assign(video, videoData);

      if (categoryIds !== undefined) {
        if (categoryIds.length > 0) {
          const categories = await this.categoryRepository.findBy({
            id: In(categoryIds),
          });
          video.categories = categories;
        } else {
          video.categories = [];
        }
      }

      if (tagNames !== undefined) {
        if (tagNames.length > 0) {
          const tags = await this.findOrCreateTags(tagNames);
          video.tags = tags;
        } else {
          video.tags = [];
        }
      }

      const updatedVideo = await this.videoRepository.save(video);
      const result = await this.findOne(updatedVideo.id);

      return {
        status: true,
        message: 'Video updated successfully',
        data: result,
      };
    } catch (error) {
      console.error(error);
      return {
        status: false,
        message: error.message || 'Failed to update video',
      };
    }
  }

  async updateStatus(id: string, status: VideoStatus, userId?: string) {
    const video = await this.videoRepository.findOne({
      where: { id },
      relations: ['uploader'],
    });

    if (!video) {
      throw new NotFoundException('Video not found');
    }

    video.status = status;

    if (status === VideoStatus.APPROVED && userId) {
      video.approvedBy = userId;
      video.approvedAt = new Date();
    }

    if (status === VideoStatus.PUBLISHED && !video.releaseDate) {
      video.releaseDate = new Date();
    }

    await this.videoRepository.save(video);
    return { message: 'Video status updated successfully' };
  }

  async incrementViewCount(id: string) {
    await this.videoRepository.increment({ id }, 'viewCount', 1);
  }

  async getFeaturedVideos(limit: number = 10) {
    return this.videoRepository.find({
      where: {
        status: VideoStatus.PUBLISHED,
        isFeatured: true,
      },
      relations: ['uploader', 'categories', 'tags'],
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async getTrendingVideos(limit: number = 10) {
    const trendingVidesos = await this.videoRepository.find({
      where: {
        status: VideoStatus.PUBLISHED,
        isTrending: true,
      },
      relations: ['uploader', 'categories', 'tags'],
      order: { viewCount: 'DESC' },
      take: limit,
    });
    if (trendingVidesos.length === 0) {
      // throw new NotFoundException('`No trending videos found`');
       return ResponseWrapper(
      'Trending videos retrieved successfully',
      true,
      trendingVidesos,
      200,
    );
    }
    return ResponseWrapper(
      'Trending videos retrieved successfully',
      true,
      trendingVidesos,
      200,
    );
  }

  async getRecommendedVideos(userId: string, limit: number = 10) {
    // Simple recommendation based on user's watch history and preferences
    // In production, this would use AI/ML algorithms
    return this.videoRepository.find({
      where: {
        status: VideoStatus.PUBLISHED,
      },
      relations: ['uploader', 'categories', 'tags'],
      order: { viewCount: 'DESC' },
      take: limit,
    });
  }

  async searchVideos(query: string, paginationDto: PaginationDto) {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [videos, total] = await this.videoRepository.findAndCount({
      where: [
        { title: ILike(`%${query}%`), status: VideoStatus.PUBLISHED },
        { description: ILike(`%${query}%`), status: VideoStatus.PUBLISHED },
        { keywords: ILike(`%${query}%`), status: VideoStatus.PUBLISHED },
      ],
      relations: ['uploader', 'categories', 'tags'],
      order: { viewCount: 'DESC' },
      skip,
      take: limit,
    });

    return {
      data: videos,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async remove(id: string, userId: string) {
    const video = await this.videoRepository.findOne({
      where: { id },
      relations: ['uploader'],
    });

    if (!video) {
      throw new NotFoundException('Video not found');
    }

    // Check if user owns the video
    if (video.uploaderId !== userId) {
      throw new ForbiddenException('You can only delete your own videos');
    }

    // Soft delete by updating status
    video.status = VideoStatus.DELETED;
    await this.videoRepository.save(video);

    return { message: 'Video deleted successfully' };
  }

  async getVideoStreams(videoId: string) {
    const streams = await this.videoStreamRepository.find({
      where: { videoId },
      relations: ['video'],
    });

    if (streams.length === 0) {
      throw new NotFoundException('No streams found for this video');
    }

    return streams;
  }

  async getVideoStreamById(streamId: string) {
    const stream = await this.videoStreamRepository.findOne({
      where: { id: streamId },
      relations: ['video'],
    });

    if (!stream) {
      throw new NotFoundException('Stream not found');
    }

    return stream;
  }

  // publish create videos 
  async publishVideo(id: string, userId: string) {
    const video = await this.videoRepository.findOne({
      where: { id },
      relations: ['uploader'],
    });

    if (!video) {
      throw new NotFoundException('Video not found');
    }

    // Check if user owns the video
    if (video.uploaderId !== userId) {
      throw new ForbiddenException('You can only publish your own videos');
    }

    // Update status to PUBLISHED
    video.status = VideoStatus.PUBLISHED;
    video.publishedAt = new Date();

    await this.videoRepository.save(video);
    return { message: 'Video published successfully' };
  }

  private async findOrCreateTags(tagNames: string[]): Promise<Tag[]> {
    const tags: Tag[] = [];

    for (const tagName of tagNames) {
      let tag = await this.tagRepository.findOne({
        where: { name: tagName.toLowerCase() },
      });

      if (!tag) {
        tag = this.tagRepository.create({
          name: tagName.toLowerCase(),
          slug: tagName.toLowerCase().replace(/\s+/g, '-'),
        });
        tag = await this.tagRepository.save(tag);
      }

      // Increment usage count
      tag.usageCount += 1;
      await this.tagRepository.save(tag);

      tags.push(tag);
    }

    return tags;
  }
}
