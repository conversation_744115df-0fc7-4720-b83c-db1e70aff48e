import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Category, CategoryType } from '../entities/category.entity';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { debug } from 'node:console';
import { ResponseWrapper } from 'src/common/utils/response-wrapper.util';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
  ) {}

  // async create(createCategoryDto: CreateCategoryDto) {
  //   const { name, slug } = createCategoryDto;

  //   // Check if category with same name or slug exists
  //   const existingCategory = await this.categoryRepository.findOne({
  //     where: [{ name }, { slug }],
  //   });

  //   if (existingCategory) {
  //     if (existingCategory.name === name) {
  //       throw new ConflictException('Category name already exists');
  //     }
  //     if (existingCategory.slug === slug) {
  //       throw new ConflictException('Category slug already exists');
  //     }
  //   }

  //   const category = this.categoryRepository.create(createCategoryDto);
    
  //   return this.categoryRepository.save(category);
  // }

  async create(createCategoryDto: CreateCategoryDto) {
    try {
      const { name, slug } = createCategoryDto;

      const existingCategory = await this.categoryRepository.findOne({
        where: [{ name }, { slug }],
      });

      if (existingCategory) {
        if (existingCategory.name === name) {
          throw new ConflictException('Category name already exists');
        }
        if (existingCategory.slug === slug) {
          throw new ConflictException('Category slug already exists');
        }
      }

      const category = this.categoryRepository.create(createCategoryDto);
      await this.categoryRepository.save(category);

      return {
        status: true,
        message: 'Category created successfully',
        data: category,
      };
    } catch (error) {
      console.error(error);
      return {
        status: false,
        message: error.message || 'Failed to create category',
      };
    }
  }

  async findAll(type?: CategoryType) {

    const whereCondition = type ? { type, active: true } : { active: true };
    
    const categories = await this.categoryRepository.find({
      where: whereCondition,
      relations: ['parent', 'children'],
      order: { sortOrder: 'ASC', name: 'ASC' },
    });
    return ResponseWrapper('Categories retrieved successfully', true, categories);
  }

  async findOne(id: string) {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['parent', 'children', 'videos'],
    });

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async findBySlug(slug: string) {
    const category = await this.categoryRepository.findOne({
      where: { slug },
      relations: ['parent', 'children', 'videos'],
    });

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async update(id: string, updateCategoryDto: UpdateCategoryDto) {
    try {
      const category = await this.categoryRepository.findOne({
        where: { id },
      });

      if (!category) {
        throw new NotFoundException('Category not found');
      }

      if (updateCategoryDto.name && updateCategoryDto.name !== category.name) {
        const existingCategory = await this.categoryRepository.findOne({
          where: { name: updateCategoryDto.name },
        });
        if (existingCategory) {
          throw new ConflictException('Category name already exists');
        }
      }

      if (updateCategoryDto.slug && updateCategoryDto.slug !== category.slug) {
        const existingCategory = await this.categoryRepository.findOne({
          where: { slug: updateCategoryDto.slug },
        });
        if (existingCategory) {
          throw new ConflictException('Category slug already exists');
        }
      }

      Object.assign(category, updateCategoryDto);
      await this.categoryRepository.save(category);

      return {
        status: true,
        message: 'Category updated successfully',
        data: category,
      };
    } catch (error) {
      console.error(error);
      return {
        status: false,
        message: error.message || 'Failed to update category',
      };
    }
  }

  async remove(id: string) {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['children'],
    });

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    // Check if category has children
    if (category.children && category.children.length > 0) {
      throw new ConflictException('Cannot delete category with subcategories');
    }

    // Soft delete by setting active to false
    category.active = false;
    await this.categoryRepository.save(category);

    return { message: 'Category deleted successfully' };
  }

  async getHierarchy() {
    const categories = await this.categoryRepository.find({
      where: { active: true, parentId: undefined },
      relations: ['children'],
      order: { sortOrder: 'ASC', name: 'ASC' },
    });

    return this.buildCategoryTree(categories);
  }

  private async buildCategoryTree(categories: Category[]): Promise<any[]> {
    const result: any[] = [];

    for (const category of categories) {
      const categoryData: any = {
        id: category.id,
        name: category.name,
        slug: category.slug,
        type: category.type,
        icon: category.icon,
        color: category.color,
        sortOrder: category.sortOrder,
        children: [],
      };

      if (category.children && category.children.length > 0) {
        categoryData.children = await this.buildCategoryTree(category.children);
      }

      result.push(categoryData);
    }

    return result;
  }

  async getPopularCategories(limit: number = 10) {
    return this.categoryRepository
      .createQueryBuilder('category')
      .leftJoin('category.videos', 'video')
      .addSelect('COUNT(video.id)', 'videoCount')
      .where('category.active = :active', { active: true })
      .groupBy('category.id')
      .orderBy('videoCount', 'DESC')
      .limit(limit)
      .getMany();
  }
}
