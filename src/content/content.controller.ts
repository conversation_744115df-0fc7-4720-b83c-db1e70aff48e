import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';

import { ContentService } from './content.service';
import { CreateVideoDto } from './dto/create-video.dto';
import { UpdateVideoDto } from './dto/update-video.dto';
import { VideoFilterDto } from './dto/video-filter.dto';
import { PaginationDto } from '../common/dto/pagination.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { User } from '../entities/user.entity';
import { VideoStatus } from '../entities/video.entity';

@ApiTags('Content')
@Controller('content')
export class ContentController {
  constructor(private readonly contentService: ContentService) {}

  @Public()
  @Get()
  @ApiOperation({ summary: 'Get all published videos' })
  @ApiResponse({ status: 200, description: 'List of videos' })
  async findAll(
    @Query() paginationDto: PaginationDto,
    @Query() filterDto: VideoFilterDto,
  ) {
    return this.contentService.findAll(paginationDto, filterDto);
  }

  @Public()
  @Get('featured')
  @ApiOperation({ summary: 'Get featured videos' })
  @ApiResponse({ status: 200, description: 'List of featured videos' })
  async getFeatured(@Query('limit') limit?: number) {
    return this.contentService.getFeaturedVideos(limit);
  }

  @Public()
  @Get('trending')
  @ApiOperation({ summary: 'Get trending videos' })
  @ApiResponse({ status: 200, description: 'List of trending videos' })
  async getTrending(@Query('limit') limit?: number) {
    return this.contentService.getTrendingVideos(limit);
  }

  @UseGuards(JwtAuthGuard)
  @Get('recommended')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get recommended videos for user' })
  @ApiResponse({ status: 200, description: 'List of recommended videos' })
  async getRecommended(
    @CurrentUser() user: User,
    @Query('limit') limit?: number,
  ) {
    return this.contentService.getRecommendedVideos(user.id, limit);
  }

  @Public()
  @Get('search')
  @ApiOperation({ summary: 'Search videos' })
  @ApiResponse({ status: 200, description: 'Search results' })
  async search(
    @Query('q') query: string,
    @Query() paginationDto: PaginationDto,
  ) {
    return this.contentService.searchVideos(query, paginationDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-videos')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user uploaded videos' })
  @ApiResponse({ status: 200, description: 'List of user videos' })
  async getMyVideos(
    @CurrentUser() user: User,
    @Query() paginationDto: PaginationDto,
  ) {
    const filterDto = new VideoFilterDto();
    filterDto.uploaderId = user.id;
    filterDto.includeUnpublished = true;
    return this.contentService.findAll(paginationDto, filterDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('super_admin','Admin', 'content_publisher', 'content_aggregator')
  @Get('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all videos for admin (including unpublished)' })
  @ApiResponse({ status: 200, description: 'List of all videos' })
  async getAdminVideos(
    @Query() paginationDto: PaginationDto,
    @Query() filterDto: VideoFilterDto,
  ) {
    filterDto.includeUnpublished = true;
    return this.contentService.findAll(paginationDto, filterDto);
  }

  @Public()
  @Get(':id')
  @ApiOperation({ summary: 'Get video by ID' })
  @ApiResponse({ status: 200, description: 'Video details' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    const video = await this.contentService.findOne(id);
    
    // Increment view count
    await this.contentService.incrementViewCount(id);
    
    return video;
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(
    'content_producer',
    'content_aggregator',
    'super_admin','Admin',
    'content_creator',
    'admin',
  )
  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create new video' })
  @ApiResponse({ status: 201, description: 'Video created successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async create(
    @Body() createVideoDto: CreateVideoDto,
    @CurrentUser() user: User,
  ) {
    return this.contentService.create(createVideoDto, user.id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  // @Roles(
  //   'content_producer',
  //   'content_aggregator',
  //   'super_admin','Admin',
  //   'content_creator',
  //   'admin',
  // )
  @Post('upload')
  @ApiBearerAuth()
  @UseInterceptors(FileInterceptor('video'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload video file' })
  @ApiResponse({ status: 201, description: 'Video uploaded successfully' })
  async uploadVideo(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser() user: User,
  ) {
    // In production, this would upload to cloud storage (AWS S3, etc.)
    // and return the URL
    const videoUrl = `/uploads/${file.filename}`;
    
    return {
      videoUrl,
      originalName: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update video' })
  @ApiResponse({ status: 200, description: 'Video updated successfully' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateVideoDto: UpdateVideoDto,
    @CurrentUser() user: User,
  ) {
    return this.contentService.update(id, updateVideoDto, user.id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('content_publisher', 'super_admin','Admin',)
  @Put(':id/status')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update video status (Publisher/Admin only)' })
  @ApiResponse({ status: 200, description: 'Video status updated successfully' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiBody({ schema: { properties: { status: { type: 'string', enum: Object.values(VideoStatus) } } } })
  @ApiBody({
    description: 'Status of the video',
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          enum: Object.values(VideoStatus),
          example: VideoStatus.PUBLISHED,
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid status value' })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('status') status: VideoStatus,
    @CurrentUser() user: User,
  ) {
    return this.contentService.updateStatus(id, status, user.id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('super_admin','Admin',)
  @Put(':id/featured')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Toggle video featured status (Super Admin only)' })
  @ApiResponse({ status: 200, description: 'Video featured status updated' })
  async toggleFeatured(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('featured') featured: boolean,
  ) {
    const video = await this.contentService.findOne(id);
    const updateDto: any = { isFeatured: featured };
    return this.contentService.update(id, updateDto, video.uploaderId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete video' })
  @ApiResponse({ status: 200, description: 'Video deleted successfully' })
  @ApiResponse({ status: 404, description: 'Video not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: User,
  ) {
    return this.contentService.remove(id, user.id);
  }
}
