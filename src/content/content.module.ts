import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';

import { ContentController } from './content.controller';
import { ContentService } from './content.service';
import { CategoriesController } from './categories.controller';
import { CategoriesService } from './categories.service';
import { TagsController } from './tags.controller';
import { TagsService } from './tags.service';

import { Video } from '../entities/video.entity';
import { Category } from '../entities/category.entity';
import { Tag } from '../entities/tag.entity';
import { VideoStream } from '../entities/video-stream.entity';
import { Comment } from '../entities/comment.entity';
import { Like } from '../entities/like.entity';
import { WatchHistory } from '../entities/watch-history.entity';
import { ContentAnalytics } from '../entities/content-analytics.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Video,
      Category,
      Tag,
      VideoStream,
      Comment,
      Like,
      WatchHistory,
      ContentAnalytics,
    ]),
    MulterModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        dest: configService.get<string>('UPLOAD_PATH', './uploads'),
        limits: {
          fileSize: configService.get<number>('MAX_FILE_SIZE', 500000000), // 500MB
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [ContentController, CategoriesController, TagsController],
  providers: [ContentService, CategoriesService, TagsService],
  exports: [ContentService, CategoriesService, TagsService],
})
export class ContentModule {}
