import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

import { TagsService } from './tags.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { PaginationDto } from '../common/dto/pagination.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Public } from '../auth/decorators/public.decorator';

@ApiTags('Tags')
@Controller('tags')
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  @Public()
  @Get()
  @ApiOperation({ summary: 'Get all tags' })
  @ApiResponse({ status: 200, description: 'List of tags' })
  @ApiQuery({ name: 'search', required: false, description: 'Search tags by name or slug' })
  // @ApiQuery({ name: 'limit', required: false, description: 'Limit number of tags returned' })
  // @ApiQuery({ name: 'page', required: false, description: 'Page number for pagination' })
  // @ApiQuery({ name: 'sort', required: false, description: 'Sort order for tags' })
  // @ApiQuery({ name: 'order', required: false, description: 'Order direction (asc/desc)' })
  // @ApiQuery({ name: 'type', required: false, description: 'Filter by tag type' })
  async findAll(
    @Query() paginationDto: PaginationDto,
    @Query('search') search?: string,
  ) {
    return this.tagsService.findAll(paginationDto, search);
  }

  @Public()
  @Get('popular')
  @ApiOperation({ summary: 'Get popular tags' })
  @ApiResponse({ status: 200, description: 'List of popular tags' })
  async getPopular(@Query('limit') limit?: number) {
    return this.tagsService.getPopularTags(limit);
  }

  @Public()
  @Get('cloud')
  @ApiOperation({ summary: 'Get tag cloud data' })
  @ApiResponse({ status: 200, description: 'Tag cloud data with sizes' })
  async getTagCloud(@Query('limit') limit?: number) {
    return this.tagsService.getTagCloud(limit);
  }

  @Public()
  @Get('search')
  @ApiOperation({ summary: 'Search tags' })
  @ApiResponse({ status: 200, description: 'Search results' })
  async search(
    @Query('q') query: string,
    @Query('limit') limit?: number,
  ) {
    return this.tagsService.searchTags(query, limit);
  }

  @Public()
  @Get('slug/:slug')
  @ApiOperation({ summary: 'Get tag by slug' })
  @ApiResponse({ status: 200, description: 'Tag details' })
  @ApiResponse({ status: 404, description: 'Tag not found' })
  async findBySlug(@Param('slug') slug: string) {
    return this.tagsService.findBySlug(slug);
  }

  @Public()
  @Get(':id')
  @ApiOperation({ summary: 'Get tag by ID' })
  @ApiResponse({ status: 200, description: 'Tag details' })
  @ApiResponse({ status: 404, description: 'Tag not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.tagsService.findOne(id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('super_admin','Admin', 'content_publisher')
  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create new tag (Admin only)' })
  @ApiResponse({ status: 201, description: 'Tag created successfully' })
  @ApiResponse({ status: 409, description: 'Tag name or slug already exists' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async create(@Body() createTagDto: CreateTagDto) {
    return this.tagsService.create(createTagDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('super_admin','Admin', 'content_publisher')
  @Put(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update tag (Admin only)' })
  @ApiResponse({ status: 200, description: 'Tag updated successfully' })
  @ApiResponse({ status: 404, description: 'Tag not found' })
  @ApiResponse({ status: 409, description: 'Tag name or slug already exists' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTagDto: UpdateTagDto,
  ) {
    return this.tagsService.update(id, updateTagDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('super_admin','Admin',)
  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete tag (Super Admin only)' })
  @ApiResponse({ status: 200, description: 'Tag deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tag not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.tagsService.remove(id);
  }
}
