import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike } from 'typeorm';

import { Tag } from '../entities/tag.entity';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { PaginationDto } from '../common/dto/pagination.dto';

@Injectable()
export class TagsService {
  constructor(
    @InjectRepository(Tag)
    private tagRepository: Repository<Tag>,
  ) {}

  async create(createTagDto: CreateTagDto) {
    const { name, slug } = createTagDto;

    // Check if tag with same name or slug exists
    const existingTag = await this.tagRepository.findOne({
      where: [{ name }, { slug }],
    });

    if (existingTag) {
      if (existingTag.name === name) {
        throw new ConflictException('Tag name already exists');
      }
      if (existingTag.slug === slug) {
        throw new ConflictException('Tag slug already exists');
      }
    }

    const tag = this.tagRepository.create(createTagDto);
    return this.tagRepository.save(tag);
  }

  async findAll(paginationDto: PaginationDto, search?: string) {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.tagRepository.createQueryBuilder('tag');

    if (search) {
      queryBuilder.where('tag.name ILIKE :search', { search: `%${search}%` });
    }

    queryBuilder
      .where('tag.active = :active', { active: true })
      .orderBy('tag.usageCount', 'DESC')
      .addOrderBy('tag.name', 'ASC')
      .skip(skip)
      .take(limit);

    const [tags, total] = await queryBuilder.getManyAndCount();

    return {
      data: tags,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const tag = await this.tagRepository.findOne({
      where: { id },
      relations: ['videos'],
    });

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    return tag;
  }

  async findBySlug(slug: string) {
    const tag = await this.tagRepository.findOne({
      where: { slug },
      relations: ['videos'],
    });

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    return tag;
  }

  async update(id: string, updateTagDto: UpdateTagDto) {
    const tag = await this.tagRepository.findOne({
      where: { id },
    });

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    // Check for conflicts if name or slug is being updated
    if (updateTagDto.name && updateTagDto.name !== tag.name) {
      const existingTag = await this.tagRepository.findOne({
        where: { name: updateTagDto.name },
      });
      if (existingTag) {
        throw new ConflictException('Tag name already exists');
      }
    }

    if (updateTagDto.slug && updateTagDto.slug !== tag.slug) {
      const existingTag = await this.tagRepository.findOne({
        where: { slug: updateTagDto.slug },
      });
      if (existingTag) {
        throw new ConflictException('Tag slug already exists');
      }
    }

    Object.assign(tag, updateTagDto);
    return this.tagRepository.save(tag);
  }

  async remove(id: string) {
    const tag = await this.tagRepository.findOne({
      where: { id },
    });

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    // Soft delete by setting active to false
    tag.active = false;
    await this.tagRepository.save(tag);

    return { message: 'Tag deleted successfully' };
  }

  async getPopularTags(limit: number = 20) {
    return this.tagRepository.find({
      where: { active: true },
      order: { usageCount: 'DESC' },
      take: limit,
    });
  }

  async searchTags(query: string, limit: number = 10) {
    return this.tagRepository.find({
      where: {
        name: ILike(`%${query}%`),
        active: true,
      },
      order: { usageCount: 'DESC' },
      take: limit,
    });
  }

  async getTagCloud(limit: number = 50) {
    const tags = await this.tagRepository.find({
      where: { active: true },
      order: { usageCount: 'DESC' },
      take: limit,
    });

    // Calculate relative sizes for tag cloud
    const maxUsage = Math.max(...tags.map(tag => tag.usageCount));
    const minUsage = Math.min(...tags.map(tag => tag.usageCount));

    return tags.map(tag => ({
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      usageCount: tag.usageCount,
      size: this.calculateTagSize(tag.usageCount, minUsage, maxUsage),
    }));
  }

  private calculateTagSize(usage: number, min: number, max: number): number {
    // Calculate size between 1 and 5 based on usage
    if (max === min) return 3;
    return Math.round(1 + (4 * (usage - min)) / (max - min));
  }
}
