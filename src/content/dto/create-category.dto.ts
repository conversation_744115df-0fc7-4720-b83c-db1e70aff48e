import {
  IsString,
  <PERSON><PERSON><PERSON>al,
  IsEnum,
  IsBoolean,
  IsNumber,
  IsUUID,
  <PERSON>Length,
  MaxLength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CategoryType } from '../../entities/category.entity';

export class CreateCategoryDto {
  @ApiProperty({
    description: 'Category name',
    example: 'Faith-Based Movies',
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Category description',
    example: 'Movies that inspire faith and spiritual growth',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Category slug (URL-friendly name)',
    example: 'faith-based-movies',
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  slug: string;

  @ApiProperty({
    description: 'Category type',
    enum: CategoryType,
    example: CategoryType.GENRE,
  })
  @IsEnum(CategoryType)
  type: CategoryType;

  @ApiProperty({
    description: 'Category icon',
    example: 'fa-cross',
    required: false,
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({
    description: 'Category color (hex code)',
    example: '#4A90E2',
    required: false,
  })
  @IsOptional()
  @IsString()
  color?: string;

  @ApiProperty({
    description: 'Banner image URL',
    example: 'https://example.com/banner.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  bannerImage?: string;

  @ApiProperty({
    description: 'Category active status',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean = true;

  @ApiProperty({
    description: 'Sort order',
    example: 1,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  sortOrder?: number = 0;

  @ApiProperty({
    description: 'Parent category ID',
    example: 'uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  parentId?: string;
}
