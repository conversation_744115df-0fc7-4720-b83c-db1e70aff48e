import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsEnum,
  IsArray,
  IsBoolean,
  IsDateString,
  IsNumber,
  Min,
  Max,
  IsUUID,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { VideoType, AgeRating } from '../../entities/video.entity';

export class CreateVideoDto {
  @ApiProperty({
    description: 'Video title',
    example: 'Faith and Hope: A Journey of Belief',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Video description',
    example: 'An inspiring story about faith, hope, and the power of belief in overcoming life challenges.',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Video thumbnail URL',
    example: 'https://example.com/thumbnail.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  thumbnail?: string;

  @ApiProperty({
    description: 'Video file URL',
    example: 'https://example.com/video.mp4',
  })
  @IsString()
  videoUrl: string;

  @ApiProperty({
    description: 'Trailer URL',
    example: 'https://example.com/trailer.mp4',
    required: false,
  })
  @IsOptional()
  @IsString()
  trailerUrl?: string;

  @ApiProperty({
    description: 'Video duration in seconds',
    example: 3600,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  durationSeconds?: number;

  @ApiProperty({
    description: 'Video type',
    enum: VideoType,
    example: VideoType.MOVIE,
  })
  @IsEnum(VideoType)
  type: VideoType;

  @ApiProperty({
    description: 'Age rating',
    enum: AgeRating,
    example: AgeRating.G,
  })
  @IsEnum(AgeRating)
  ageRating: AgeRating;

  @ApiProperty({
    description: 'Video language',
    example: 'en',
    default: 'en',
  })
  @IsOptional()
  @IsString()
  language?: string = 'en';

  @ApiProperty({
    description: 'Available subtitle languages',
    example: ['en', 'yo', 'ig'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  subtitleLanguages?: string[];

  // @ApiProperty({
  //   description: 'Release date',
  //   example: '2024-01-01',
  //   required: false,
  // })
  // @IsOptional()
  // @IsDateString()
  // releaseDate?: Date;

  // @ApiProperty({
  //   description: 'Scheduled publish date',
  //   example: '2024-01-01T10:00:00Z',
  //   required: false,
  // })
  // @IsOptional()
  // @IsDateString()
  // scheduledPublishDate?: Date;

  @ApiProperty({
  description: 'Release date',
  example: '2024-01-01',
  required: false,
})
@IsOptional()
@IsDateString()
releaseDate?: string;

@ApiProperty({
  description: 'Scheduled publish date',
  example: '2024-01-01T10:00:00Z',
  required: false,
})
@IsOptional()
@IsDateString()
scheduledPublishDate?: string;


  @ApiProperty({
    description: 'Allow comments on video',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  allowComments?: boolean = true;

  @ApiProperty({
    description: 'Allow likes on video',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  allowLikes?: boolean = true;

  @ApiProperty({
    description: 'Allow sharing of video',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  allowSharing?: boolean = true;

  @ApiProperty({
    description: 'Video keywords for search',
    example: ['faith', 'hope', 'inspiration', 'christian'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @ApiProperty({
    description: 'Category IDs',
    example: ['uuid1', 'uuid2'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  categoryIds?: string[];

  @ApiProperty({
    description: 'Tag names',
    example: ['faith-based', 'inspirational', 'family-friendly'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tagNames?: string[];
}
