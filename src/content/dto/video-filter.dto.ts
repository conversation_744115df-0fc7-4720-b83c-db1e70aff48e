import { IsOptional, IsEnum, IsString, IsUUID, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { VideoStatus, VideoType } from '../../entities/video.entity';

export class VideoFilterDto {
  @ApiProperty({
    description: 'Search query',
    example: 'faith',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Video status filter',
    enum: VideoStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(VideoStatus)
  status?: VideoStatus;

  @ApiProperty({
    description: 'Video type filter',
    enum: VideoType,
    required: false,
  })
  @IsOptional()
  @IsEnum(VideoType)
  type?: VideoType;

  @ApiProperty({
    description: 'Category ID filter',
    example: 'uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiProperty({
    description: 'Language filter',
    example: 'en',
    required: false,
  })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({
    description: 'Uploader ID filter',
    example: 'uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  uploaderId?: string;

  @ApiProperty({
    description: 'Sort by field',
    example: 'createdAt',
    default: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({
    description: 'Sort order',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
    default: 'DESC',
    required: false,
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @ApiProperty({
    description: 'Include unpublished videos (admin only)',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  includeUnpublished?: boolean = false;
}
