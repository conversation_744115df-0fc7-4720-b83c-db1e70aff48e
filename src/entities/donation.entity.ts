import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

export enum DonationStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled',
}

export enum DonationType {
  ONE_TIME = 'one_time',
  RECURRING = 'recurring',
}

@Entity('donations')
export class Donation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'donor_id' })
  donorId: string;

  @Column({ nullable: true, name: 'recipient_id' })
  recipientId: string;

  @Column({ nullable: true, name: 'content_creator_id' })
  contentCreatorId: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ default: 'USD' })
  currency: string;

  @Column({
    type: 'enum',
    enum: DonationType,
    default: DonationType.ONE_TIME,
  })
  type: DonationType;

  @Column({
    type: 'enum',
    enum: DonationStatus,
    default: DonationStatus.PENDING,
  })
  status: DonationStatus;

  @Column({ nullable: true, name: 'payment_method' })
  paymentMethod: string;

  @Column({ nullable: true, name: 'payment_provider' })
  paymentProvider: string;

  @Column({ nullable: true, name: 'transaction_id' })
  transactionId: string;

  @Column({ nullable: true, name: 'provider_transaction_id' })
  providerTransactionId: string;

  @Column('text', { nullable: true })
  message: string;

  @Column({ default: false, name: 'is_anonymous' })
  isAnonymous: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, name: 'platform_fee_percentage' })
  platformFeePercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'platform_fee_amount' })
  platformFeeAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'net_amount' })
  netAmount: number;

  @Column({ nullable: true, name: 'processed_at' })
  processedAt: Date;

  @Column({ nullable: true, name: 'failed_reason' })
  failedReason: string;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.donations)
  @JoinColumn({ name: 'donor_id' })
  donor: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'recipient_id' })
  recipient: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'content_creator_id' })
  contentCreator: User;
}
