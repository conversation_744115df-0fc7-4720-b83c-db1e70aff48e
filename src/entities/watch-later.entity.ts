import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { User } from './user.entity';
import { Video } from './video.entity';

@Entity('watch_later')
@Unique(['userId', 'videoId'])
export class WatchLater {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'video_id' })
  videoId: string;

  @ManyToOne(() => User, (user) => user.watchLater)
  @JoinColumn({ name: 'user_id' })
  user: User;


}
