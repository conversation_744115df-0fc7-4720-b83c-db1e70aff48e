import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto',
}

export enum Language {
  ENGLISH = 'en',
  YORUBA = 'yo',
  IGBO = 'ig',
  HAUSA = 'ha',
  FRENCH = 'fr',
  SPANISH = 'es',
}

@Entity('user_profiles')
export class UserProfile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ nullable: true })
  avatar: string;

  @Column({ nullable: true })
  bio: string;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true, name: 'date_of_birth' })
  dateOfBirth: Date;

  @Column({ nullable: true })
  gender: string;

  @Column({
    type: 'enum',
    enum: Theme,
    default: Theme.AUTO,
  })
  theme: Theme;

  @Column({
    type: 'enum',
    enum: Language,
    default: Language.ENGLISH,
  })
  language: Language;

  @Column({ default: true, name: 'email_notifications' })
  emailNotifications: boolean;

  @Column({ default: true, name: 'push_notifications' })
  pushNotifications: boolean;

  @Column({ default: false, name: 'marketing_emails' })
  marketingEmails: boolean;

  @Column({ default: true, name: 'ad_personalization' })
  adPersonalization: boolean;

  @Column({ default: false, name: 'parental_controls' })
  parentalControls: boolean;

  @Column({ nullable: true, name: 'parental_pin' })
  parentalPin: string;

  @Column('text', { array: true, default: '{}', name: 'preferred_genres' })
  preferredGenres: string[];

  @Column('text', { array: true, default: '{}', name: 'preferred_languages' })
  preferredLanguages: string[];

  @Column({ default: 0, name: 'watch_time_minutes' })
  watchTimeMinutes: number;

  @Column({ default: 0, name: 'videos_watched' })
  videosWatched: number;

  @Column({ default: 0, name: 'referral_count' })
  referralCount: number;

  @Column({ default: 0, name: 'reward_points' })
  rewardPoints: number;

  @Column('jsonb', { nullable: true })
  preferences: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @OneToOne(() => User, (user) => user.profile)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
