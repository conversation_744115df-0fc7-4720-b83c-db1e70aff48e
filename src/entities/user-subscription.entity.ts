import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { User } from './user.entity';
import { SubscriptionPlan } from './subscription-plan.entity';
import { PaymentTransaction } from './payment-transaction.entity';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  TRIALING = 'trialing',
  PAST_DUE = 'past_due',
  CANCELED = 'canceled',
  UNPAID = 'unpaid',
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',
  PAUSED = 'paused',
  EXPIRED = 'expired',
}

export enum SubscriptionCancelReason {
  USER_REQUESTED = 'user_requested',
  PAYMENT_FAILED = 'payment_failed',
  FRAUD = 'fraud',
  ADMIN_ACTION = 'admin_action',
  DOWNGRADE = 'downgrade',
  UPGRADE = 'upgrade',
  OTHER = 'other',
}

@Entity('user_subscriptions')
export class UserSubscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'plan_id' })
  planId: string;

  @Column({
    type: 'enum',
    enum: SubscriptionStatus,
    default: SubscriptionStatus.ACTIVE,
  })
  status: SubscriptionStatus;

  @Column({ name: 'current_period_start' })
  currentPeriodStart: Date;

  @Column({ name: 'current_period_end' })
  currentPeriodEnd: Date;

  @Column({ nullable: true, name: 'trial_start' })
  trialStart: Date;

  @Column({ nullable: true, name: 'trial_end' })
  trialEnd: Date;

  @Column({ nullable: true, name: 'canceled_at' })
  canceledAt: Date;

  @Column({ nullable: true, name: 'ended_at' })
  endedAt: Date;

  @Column({ nullable: true, name: 'paused_at' })
  pausedAt: Date;

  @Column({ nullable: true, name: 'resumed_at' })
  resumedAt: Date;

  @Column({
    type: 'enum',
    enum: SubscriptionCancelReason,
    nullable: true,
    name: 'cancel_reason',
  })
  cancelReason: SubscriptionCancelReason;

  @Column({ nullable: true, name: 'cancel_reason_details' })
  cancelReasonDetails: string;

  @Column({ default: true, name: 'auto_renew' })
  autoRenew: boolean;

  @Column({ type: 'int', default: 0, name: 'billing_cycle_count' })
  billingCycleCount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, name: 'amount_paid' })
  amountPaid: number;

  @Column({ default: 'USD' })
  currency: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, name: 'discount_percentage' })
  discountPercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'discount_amount' })
  discountAmount: number;

  @Column({ nullable: true, name: 'coupon_code' })
  couponCode: string;

  @Column({ nullable: true, name: 'promo_code' })
  promoCode: string;

  // Payment provider information
  @Column({ nullable: true, name: 'stripe_subscription_id' })
  stripeSubscriptionId: string;

  @Column({ nullable: true, name: 'stripe_customer_id' })
  stripeCustomerId: string;

  @Column({ nullable: true, name: 'paypal_subscription_id' })
  paypalSubscriptionId: string;

  @Column({ nullable: true, name: 'paypal_payer_id' })
  paypalPayerId: string;

  @Column({ nullable: true, name: 'apple_transaction_id' })
  appleTransactionId: string;

  @Column({ nullable: true, name: 'google_purchase_token' })
  googlePurchaseToken: string;

  @Column({ nullable: true, name: 'paystack_subscription_code' })
  paystackSubscriptionCode: string;

  @Column({ nullable: true, name: 'paystack_customer_code' })
  paystackCustomerCode: string;

  @Column({ nullable: true, name: 'flutterwave_subscription_id' })
  flutterwaveSubscriptionId: string;

  @Column({ nullable: true, name: 'flutterwave_customer_id' })
  flutterwaveCustomerId: string;

  // Billing information
  @Column({ nullable: true, name: 'next_billing_date' })
  nextBillingDate: Date;

  @Column({ nullable: true, name: 'last_billing_date' })
  lastBillingDate: Date;

  @Column({ type: 'int', default: 0, name: 'failed_payment_attempts' })
  failedPaymentAttempts: number;

  @Column({ nullable: true, name: 'last_payment_error' })
  lastPaymentError: string;

  @Column({ default: false, name: 'payment_retry_enabled' })
  paymentRetryEnabled: boolean;

  // Usage tracking
  @Column({ type: 'int', default: 0, name: 'concurrent_streams_used' })
  concurrentStreamsUsed: number;

  @Column({ type: 'int', default: 0, name: 'downloads_used' })
  downloadsUsed: number;

  @Column({ type: 'int', default: 0, name: 'profiles_created' })
  profilesCreated: number;

  // Notifications
  @Column({ default: true, name: 'renewal_reminder_sent' })
  renewalReminderSent: boolean;

  @Column({ default: false, name: 'cancellation_survey_sent' })
  cancellationSurveySent: boolean;

  @Column({ default: false, name: 'win_back_campaign_sent' })
  winBackCampaignSent: boolean;

  // Additional metadata
  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @Column('jsonb', { nullable: true, name: 'billing_address' })
  billingAddress: Record<string, any>;

  @Column('jsonb', { nullable: true, name: 'tax_info' })
  taxInfo: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.subscriptions)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => SubscriptionPlan, (plan) => plan.subscriptions)
  @JoinColumn({ name: 'plan_id' })
  plan: SubscriptionPlan;

  @OneToMany(() => PaymentTransaction, (transaction) => transaction.subscription)
  transactions: PaymentTransaction[];

  // Virtual properties
  get isActive(): boolean {
    return this.status === SubscriptionStatus.ACTIVE;
  }

  get isTrialing(): boolean {
    return this.status === SubscriptionStatus.TRIALING;
  }

  get isCanceled(): boolean {
    return this.status === SubscriptionStatus.CANCELED;
  }

  get isPastDue(): boolean {
    return this.status === SubscriptionStatus.PAST_DUE;
  }

  get isExpired(): boolean {
    return this.status === SubscriptionStatus.EXPIRED || 
           (this.currentPeriodEnd && new Date() > this.currentPeriodEnd);
  }

  get daysUntilRenewal(): number {
    if (!this.nextBillingDate) return 0;
    const now = new Date();
    const diff = this.nextBillingDate.getTime() - now.getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  }

  get daysInTrial(): number {
    if (!this.trialStart || !this.trialEnd) return 0;
    const diff = this.trialEnd.getTime() - this.trialStart.getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  }

  get trialDaysRemaining(): number {
    if (!this.trialEnd || this.status !== SubscriptionStatus.TRIALING) return 0;
    const now = new Date();
    const diff = this.trialEnd.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
  }
}
