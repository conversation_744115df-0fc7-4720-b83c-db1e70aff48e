import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { User } from './user.entity';
import { PaymentTransaction } from './payment-transaction.entity';

export enum PaymentMethodType {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_ACCOUNT = 'bank_account',
  PAYPAL = 'paypal',
  APPLE_PAY = 'apple_pay',
  GOOGLE_PAY = 'google_pay',
  CRYPTO_WALLET = 'crypto_wallet',
  GIFT_CARD = 'gift_card',
}

export enum PaymentMethodStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
  REQUIRES_VERIFICATION = 'requires_verification',
  FAILED_VERIFICATION = 'failed_verification',
}

export enum CardBrand {
  VISA = 'visa',
  MASTERCARD = 'mastercard',
  AMERICAN_EXPRESS = 'amex',
  DISCOVER = 'discover',
  DINERS_CLUB = 'diners',
  JCB = 'jcb',
  UNIONPAY = 'unionpay',
  UNKNOWN = 'unknown',
}

@Entity('payment_methods')
export class PaymentMethod {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({
    type: 'enum',
    enum: PaymentMethodType,
  })
  type: PaymentMethodType;

  @Column({
    type: 'enum',
    enum: PaymentMethodStatus,
    default: PaymentMethodStatus.ACTIVE,
  })
  status: PaymentMethodStatus;

  @Column({ default: false, name: 'is_default' })
  isDefault: boolean;

  @Column({ nullable: true })
  nickname: string;

  // Provider information
  @Column({ nullable: true, name: 'stripe_payment_method_id' })
  stripePaymentMethodId: string;

  @Column({ nullable: true, name: 'paypal_payer_id' })
  paypalPayerId: string;

  @Column({ nullable: true, name: 'apple_pay_device_id' })
  applePayDeviceId: string;

  @Column({ nullable: true, name: 'google_pay_token' })
  googlePayToken: string;

  // Card information (masked/tokenized)
  @Column({
    type: 'enum',
    enum: CardBrand,
    nullable: true,
  })
  cardBrand: CardBrand;

  @Column({ nullable: true, name: 'card_last_four' })
  cardLastFour: string;

  @Column({ nullable: true, name: 'card_exp_month' })
  cardExpMonth: string;

  @Column({ nullable: true, name: 'card_exp_year' })
  cardExpYear: string;

  @Column({ nullable: true, name: 'card_fingerprint' })
  cardFingerprint: string;

  @Column({ nullable: true, name: 'card_country' })
  cardCountry: string;

  @Column({ nullable: true, name: 'card_funding' })
  cardFunding: string; // credit, debit, prepaid, unknown

  // Bank account information (masked)
  @Column({ nullable: true, name: 'bank_name' })
  bankName: string;

  @Column({ nullable: true, name: 'bank_account_type' })
  bankAccountType: string; // checking, savings

  @Column({ nullable: true, name: 'bank_account_last_four' })
  bankAccountLastFour: string;

  @Column({ nullable: true, name: 'bank_routing_number' })
  bankRoutingNumber: string;

  @Column({ nullable: true, name: 'bank_country' })
  bankCountry: string;

  // Billing address
  @Column({ nullable: true, name: 'billing_name' })
  billingName: string;

  @Column({ nullable: true, name: 'billing_email' })
  billingEmail: string;

  @Column({ nullable: true, name: 'billing_phone' })
  billingPhone: string;

  @Column({ nullable: true, name: 'billing_address_line1' })
  billingAddressLine1: string;

  @Column({ nullable: true, name: 'billing_address_line2' })
  billingAddressLine2: string;

  @Column({ nullable: true, name: 'billing_city' })
  billingCity: string;

  @Column({ nullable: true, name: 'billing_state' })
  billingState: string;

  @Column({ nullable: true, name: 'billing_postal_code' })
  billingPostalCode: string;

  @Column({ nullable: true, name: 'billing_country' })
  billingCountry: string;

  // Verification
  @Column({ default: false, name: 'is_verified' })
  isVerified: boolean;

  @Column({ nullable: true, name: 'verification_method' })
  verificationMethod: string;

  @Column({ nullable: true, name: 'verification_date' })
  verificationDate: Date;

  @Column({ nullable: true, name: 'verification_failure_reason' })
  verificationFailureReason: string;

  // Usage tracking
  @Column({ type: 'int', default: 0, name: 'usage_count' })
  usageCount: number;

  @Column({ nullable: true, name: 'last_used_at' })
  lastUsedAt: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'total_amount_processed' })
  totalAmountProcessed: number;

  // Security
  @Column({ default: false, name: 'is_compromised' })
  isCompromised: boolean;

  @Column({ nullable: true, name: 'compromised_at' })
  compromisedAt: Date;

  @Column({ nullable: true, name: 'compromised_reason' })
  compromisedReason: string;

  @Column({ type: 'int', default: 0, name: 'failed_attempts' })
  failedAttempts: number;

  @Column({ nullable: true, name: 'last_failure_at' })
  lastFailureAt: Date;

  // Expiration handling
  @Column({ nullable: true, name: 'expires_at' })
  expiresAt: Date;

  @Column({ default: false, name: 'expiry_notification_sent' })
  expiryNotificationSent: boolean;

  @Column({ default: false, name: 'auto_update_enabled' })
  autoUpdateEnabled: boolean;

  // Additional metadata
  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @Column('jsonb', { nullable: true, name: 'provider_data' })
  providerData: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.paymentMethods)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => PaymentTransaction, (transaction) => transaction.paymentMethod)
  transactions: PaymentTransaction[];

  // Virtual properties
  get isActive(): boolean {
    return this.status === PaymentMethodStatus.ACTIVE;
  }

  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  get isCard(): boolean {
    return this.type === PaymentMethodType.CREDIT_CARD || this.type === PaymentMethodType.DEBIT_CARD;
  }

  get isBankAccount(): boolean {
    return this.type === PaymentMethodType.BANK_ACCOUNT;
  }

  get isDigitalWallet(): boolean {
    return [
      PaymentMethodType.PAYPAL,
      PaymentMethodType.APPLE_PAY,
      PaymentMethodType.GOOGLE_PAY,
    ].includes(this.type);
  }

  get displayName(): string {
    if (this.nickname) return this.nickname;
    
    if (this.isCard && this.cardBrand && this.cardLastFour) {
      return `${this.cardBrand.toUpperCase()} •••• ${this.cardLastFour}`;
    }
    
    if (this.isBankAccount && this.bankName && this.bankAccountLastFour) {
      return `${this.bankName} •••• ${this.bankAccountLastFour}`;
    }
    
    if (this.type === PaymentMethodType.PAYPAL) {
      return 'PayPal';
    }
    
    return this.type.replace('_', ' ').toUpperCase();
  }

  get maskedNumber(): string {
    if (this.isCard && this.cardLastFour) {
      return `•••• •••• •••• ${this.cardLastFour}`;
    }
    
    if (this.isBankAccount && this.bankAccountLastFour) {
      return `•••• •••• •••• ${this.bankAccountLastFour}`;
    }
    
    return '';
  }

  get isExpiringSoon(): boolean {
    if (!this.expiresAt) return false;
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    return this.expiresAt <= thirtyDaysFromNow;
  }
}
