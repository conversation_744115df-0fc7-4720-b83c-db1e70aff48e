import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Video } from './video.entity';

export enum ContentEvent {
  VIEW = 'view',
  LIKE = 'like',
  DISLIKE = 'dislike',
  COMMENT = 'comment',
  SHARE = 'share',
  WATCH_TIME = 'watch_time',
  COMPLETION = 'completion',
  SKIP = 'skip',
  SEARCH_RESULT = 'search_result',
  RECOMMENDATION_CLICK = 'recommendation_click',
}

@Entity('content_analytics')
@Index(['videoId', 'date'])
export class ContentAnalytics {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'video_id' })
  videoId: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({
    type: 'enum',
    enum: ContentEvent,
  })
  event: ContentEvent;

  @Column({ default: 0 })
  count: number;

  @Column({ default: 0, name: 'total_value' })
  totalValue: number;

  @Column({ nullable: true, name: 'user_id' })
  userId: string;

  @Column({ nullable: true, name: 'device_type' })
  deviceType: string;

  @Column({ nullable: true })
  platform: string;

  @Column({ nullable: true })
  region: string;

  @Column({ nullable: true, name: 'traffic_source' })
  trafficSource: string;

  @Column({ nullable: true, name: 'referrer' })
  referrer: string;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => Video)
  @JoinColumn({ name: 'video_id' })
  video: Video;
}
