import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { AdCampaign } from './ad-campaign.entity';

export enum AdType {
  PRE_ROLL = 'pre_roll',
  MID_ROLL = 'mid_roll',
  POST_ROLL = 'post_roll',
  BANNER = 'banner',
  SIDEBAR = 'sidebar',
  SPONSORED_CONTENT = 'sponsored_content',
}

export enum AdStatus {
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  ACTIVE = 'active',
  PAUSED = 'paused',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
}

@Entity('ads')
export class Ad {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({
    type: 'varchar',
  })
  type: AdType;

  @Column({
    type: 'enum',
    enum: AdStatus,
    default: AdStatus.DRAFT,
  })
  status: AdStatus;

  @Column({ name: 'creative_url' })
  creativeUrl: string;

  @Column({ nullable: true, name: 'click_url' })
  clickUrl: string;

  @Column({ nullable: true, name: 'call_to_action' })
  callToAction: string;

  @Column({ default: 0, name: 'duration_seconds' })
  durationSeconds: number;

  @Column({ default: false, name: 'skippable' })
  skippable: boolean;

  @Column({ default: 5, name: 'skip_after_seconds' })
  skipAfterSeconds: number;

  @Column('text', { array: true, default: '{}', name: 'target_regions' })
  targetRegions: string[];

  @Column('text', { array: true, default: '{}', name: 'target_devices' })
  targetDevices: string[];

  @Column('text', { array: true, default: '{}', name: 'target_interests' })
  targetInterests: string[];

  @Column('text', { array: true, default: '{}', name: 'target_content_types' })
  targetContentTypes: string[];

  @Column({ nullable: true, name: 'start_date' })
  startDate: Date;

  @Column({ nullable: true, name: 'end_date' })
  endDate: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  budget: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, name: 'cost_per_view' })
  costPerView: number;

  @Column({ default: 0, name: 'max_frequency' })
  maxFrequency: number;

  @Column({ name: 'advertiser_id' })
  advertiserId: string;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @OneToMany(() => AdCampaign, (campaign) => campaign.ad)
  campaigns: AdCampaign[];
}
