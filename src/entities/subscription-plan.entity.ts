import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { UserSubscription } from './user-subscription.entity';

export enum SubscriptionPlanType {
  FREE = 'free',
  BASIC = 'basic',
  PREMIUM = 'premium',
  FAMILY = 'family',
  ANNUAL = 'annual',
}

export enum SubscriptionPlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEPRECATED = 'deprecated',
}

export enum BillingInterval {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  LIFETIME = 'lifetime',
}

@Entity('subscription_plans')
export class SubscriptionPlan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string;

  @Column()
  description: string;

  @Column({ unique: true })
  slug: string;

  @Column({
    type: 'enum',
    enum: SubscriptionPlanType,
  })
  type: SubscriptionPlanType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ default: 'USD' })
  currency: string;

  @Column({
    type: 'enum',
    enum: BillingInterval,
    default: BillingInterval.MONTHLY,
  })
  billingInterval: BillingInterval;

  @Column({ type: 'int', default: 1, name: 'billing_interval_count' })
  billingIntervalCount: number;

  @Column({ type: 'int', nullable: true, name: 'trial_period_days' })
  trialPeriodDays: number;

  @Column({
    type: 'enum',
    enum: SubscriptionPlanStatus,
    default: SubscriptionPlanStatus.ACTIVE,
  })
  status: SubscriptionPlanStatus;

  @Column({ type: 'int', default: 0, name: 'sort_order' })
  sortOrder: number;

  @Column({ default: false, name: 'is_featured' })
  isFeatured: boolean;

  @Column({ default: false, name: 'is_popular' })
  isPopular: boolean;

  // Features
  @Column({ type: 'int', nullable: true, name: 'max_concurrent_streams' })
  maxConcurrentStreams: number;

  @Column({ type: 'int', nullable: true, name: 'max_download_quality' })
  maxDownloadQuality: number;

  @Column({ default: true, name: 'hd_streaming' })
  hdStreaming: boolean;

  @Column({ default: false, name: 'uhd_streaming' })
  uhdStreaming: boolean;

  @Column({ default: false, name: 'offline_downloads' })
  offlineDownloads: boolean;

  @Column({ default: false, name: 'ad_free' })
  adFree: boolean;

  @Column({ default: false, name: 'early_access' })
  earlyAccess: boolean;

  @Column({ default: false, name: 'exclusive_content' })
  exclusiveContent: boolean;

  @Column({ default: false, name: 'live_streaming' })
  liveStreaming: boolean;

  @Column({ default: false, name: 'priority_support' })
  prioritySupport: boolean;

  @Column({ type: 'int', nullable: true, name: 'max_profiles' })
  maxProfiles: number;

  @Column({ default: false, name: 'parental_controls' })
  parentalControls: boolean;

  @Column({ default: false, name: 'custom_playlists' })
  customPlaylists: boolean;

  @Column({ type: 'int', nullable: true, name: 'max_playlist_items' })
  maxPlaylistItems: number;

  // Payment provider IDs
  @Column({ nullable: true, name: 'stripe_price_id' })
  stripePriceId: string;

  @Column({ nullable: true, name: 'paypal_plan_id' })
  paypalPlanId: string;

  @Column({ nullable: true, name: 'apple_product_id' })
  appleProductId: string;

  @Column({ nullable: true, name: 'google_product_id' })
  googleProductId: string;

  @Column({ nullable: true, name: 'paystack_plan_code' })
  paystackPlanCode: string;

  @Column({ nullable: true, name: 'flutterwave_plan_id' })
  flutterwavePlanId: string;

  // Additional metadata
  @Column('jsonb', { nullable: true })
  features: string[];

  @Column('jsonb', { nullable: true })
  limitations: Record<string, any>;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @OneToMany(() => UserSubscription, (subscription) => subscription.plan)
  subscriptions: UserSubscription[];

  // Virtual properties
  get isActive(): boolean {
    return this.status === SubscriptionPlanStatus.ACTIVE;
  }

  get isFree(): boolean {
    return this.type === SubscriptionPlanType.FREE || this.price === 0;
  }

  get monthlyPrice(): number {
    if (this.billingInterval === BillingInterval.MONTHLY) {
      return this.price;
    } else if (this.billingInterval === BillingInterval.YEARLY) {
      return this.price / 12;
    } else if (this.billingInterval === BillingInterval.QUARTERLY) {
      return this.price / 3;
    }
    return this.price;
  }

  get yearlyPrice(): number {
    if (this.billingInterval === BillingInterval.YEARLY) {
      return this.price;
    } else if (this.billingInterval === BillingInterval.MONTHLY) {
      return this.price * 12;
    } else if (this.billingInterval === BillingInterval.QUARTERLY) {
      return this.price * 4;
    }
    return this.price * 12;
  }
}
