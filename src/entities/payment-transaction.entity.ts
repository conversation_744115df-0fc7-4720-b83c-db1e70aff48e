import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { UserSubscription } from './user-subscription.entity';
import { PaymentMethod } from './payment-method.entity';

export enum TransactionType {
  SUBSCRIPTION = 'subscription',
  DONATION = 'donation',
  ONE_TIME_PURCHASE = 'one_time_purchase',
  REFUND = 'refund',
  CHARGEBACK = 'chargeback',
  ADJUSTMENT = 'adjustment',
  CREDIT = 'credit',
  PAYOUT = 'payout',
}

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELED = 'canceled',
  REFUNDED = 'refunded',
  DISPUTED = 'disputed',
  EXPIRED = 'expired',
}

export enum PaymentProvider {
  STRIPE = 'stripe',
  PAYPAL = 'paypal',
  PAYSTACK = 'paystack',
  FLUTTERWAVE = 'flutterwave',
  APPLE_PAY = 'apple_pay',
  GOOGLE_PAY = 'google_pay',
  BANK_TRANSFER = 'bank_transfer',
  CREDIT_CARD = 'credit_card',
  CRYPTO = 'crypto',
  MANUAL = 'manual',
}

@Entity('payment_transactions')
export class PaymentTransaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ nullable: true, name: 'subscription_id' })
  subscriptionId: string;

  @Column({ nullable: true, name: 'payment_method_id' })
  paymentMethodId: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
  })
  type: TransactionType;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ default: 'USD' })
  currency: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'fee_amount' })
  feeAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'tax_amount' })
  taxAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, name: 'net_amount' })
  netAmount: number;

  @Column({
    type: 'enum',
    enum: PaymentProvider,
  })
  provider: PaymentProvider;

  @Column({ nullable: true, name: 'provider_transaction_id' })
  providerTransactionId: string;

  @Column({ nullable: true, name: 'paystack_reference' })
  paystackReference: string;

  @Column({ nullable: true, name: 'paystack_customer_code' })
  paystackCustomerCode: string;

  @Column({ nullable: true, name: 'flutterwave_tx_ref' })
  flutterwaveTxRef: string;

  @Column({ nullable: true, name: 'flutterwave_flw_ref' })
  flutterwaveFlwRef: string;

  @Column({ nullable: true, name: 'provider_payment_intent_id' })
  providerPaymentIntentId: string;

  @Column({ nullable: true, name: 'provider_customer_id' })
  providerCustomerId: string;

  @Column({ nullable: true, name: 'provider_subscription_id' })
  providerSubscriptionId: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true, name: 'invoice_number' })
  invoiceNumber: string;

  @Column({ nullable: true, name: 'receipt_url' })
  receiptUrl: string;

  @Column({ nullable: true, name: 'invoice_url' })
  invoiceUrl: string;

  // Billing information
  @Column('jsonb', { nullable: true, name: 'billing_address' })
  billingAddress: Record<string, any>;

  @Column('jsonb', { nullable: true, name: 'shipping_address' })
  shippingAddress: Record<string, any>;

  // Payment method details (masked)
  @Column({ nullable: true, name: 'payment_method_type' })
  paymentMethodType: string;

  @Column({ nullable: true, name: 'card_last_four' })
  cardLastFour: string;

  @Column({ nullable: true, name: 'card_brand' })
  cardBrand: string;

  @Column({ nullable: true, name: 'card_exp_month' })
  cardExpMonth: string;

  @Column({ nullable: true, name: 'card_exp_year' })
  cardExpYear: string;

  @Column({ nullable: true, name: 'bank_name' })
  bankName: string;

  @Column({ nullable: true, name: 'bank_account_last_four' })
  bankAccountLastFour: string;

  // Timing information
  @Column({ nullable: true, name: 'processed_at' })
  processedAt: Date;

  @Column({ nullable: true, name: 'failed_at' })
  failedAt: Date;

  @Column({ nullable: true, name: 'refunded_at' })
  refundedAt: Date;

  @Column({ nullable: true, name: 'disputed_at' })
  disputedAt: Date;

  // Error handling
  @Column({ nullable: true, name: 'failure_code' })
  failureCode: string;

  @Column({ nullable: true, name: 'failure_message' })
  failureMessage: string;

  @Column({ type: 'int', default: 0, name: 'retry_count' })
  retryCount: number;

  @Column({ nullable: true, name: 'next_retry_at' })
  nextRetryAt: Date;

  // Refund information
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'refunded_amount' })
  refundedAmount: number;

  @Column({ nullable: true, name: 'refund_reason' })
  refundReason: string;

  @Column({ nullable: true, name: 'refund_transaction_id' })
  refundTransactionId: string;

  // Risk and fraud
  @Column({ type: 'decimal', precision: 3, scale: 2, nullable: true, name: 'risk_score' })
  riskScore: number;

  @Column({ nullable: true, name: 'risk_level' })
  riskLevel: string;

  @Column({ default: false, name: 'is_disputed' })
  isDisputed: boolean;

  @Column({ default: false, name: 'is_fraudulent' })
  isFraudulent: boolean;

  // Subscription specific
  @Column({ nullable: true, name: 'billing_period_start' })
  billingPeriodStart: Date;

  @Column({ nullable: true, name: 'billing_period_end' })
  billingPeriodEnd: Date;

  @Column({ default: false, name: 'is_trial_payment' })
  isTrialPayment: boolean;

  @Column({ default: false, name: 'is_proration' })
  isProration: boolean;

  // Discounts and coupons
  @Column({ nullable: true, name: 'coupon_code' })
  couponCode: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'discount_amount' })
  discountAmount: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, name: 'discount_percentage' })
  discountPercentage: number;

  // Additional metadata
  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @Column('jsonb', { nullable: true, name: 'provider_data' })
  providerData: Record<string, any>;

  @Column('jsonb', { nullable: true, name: 'webhook_data' })
  webhookData: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.transactions)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => UserSubscription, (subscription) => subscription.transactions)
  @JoinColumn({ name: 'subscription_id' })
  subscription: UserSubscription;

  @ManyToOne(() => PaymentMethod, (paymentMethod) => paymentMethod.transactions)
  @JoinColumn({ name: 'payment_method_id' })
  paymentMethod: PaymentMethod;

  // Virtual properties
  get isSuccessful(): boolean {
    return this.status === TransactionStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === TransactionStatus.FAILED;
  }

  get isPending(): boolean {
    return this.status === TransactionStatus.PENDING || this.status === TransactionStatus.PROCESSING;
  }

  get isRefunded(): boolean {
    return this.status === TransactionStatus.REFUNDED || this.refundedAmount > 0;
  }

  get totalAmount(): number {
    return this.amount + this.taxAmount + this.feeAmount;
  }

  get effectiveAmount(): number {
    return this.amount - this.discountAmount;
  }
}
