import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Category } from './category.entity';
import { Tag } from './tag.entity';
import { WatchHistory } from './watch-history.entity';
import { Comment } from './comment.entity';
import { Like } from './like.entity';
import { VideoStream } from './video-stream.entity';

export enum VideoStatus {
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  PUBLISHED = 'published',
  REJECTED = 'rejected',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

export enum VideoType {
  MOVIE = 'movie',
  SERIES_EPISODE = 'series_episode',
  DOCUMENTARY = 'documentary',
  LIVE_EVENT = 'live_event',
  SHORT_FILM = 'short_film',
  MUSIC_VIDEO = 'music_video',
  SERMON = 'sermon',
  TESTIMONY = 'testimony',
}

export enum AgeRating {
  G = 'G',        // General Audiences
  PG = 'PG',      // Parental Guidance
  PG13 = 'PG-13', // Parents Strongly Cautioned
  R = 'R',        // Restricted
  NC17 = 'NC-17', // Adults Only
}

@Entity('videos')
export class Video {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('text', { array: true, default: '{}' })
  thumbnails: string[];

  @Column({ name: 'video_url' })
  videoUrl: string;

  @Column({ nullable: true, name: 'trailer_url' })
  trailerUrl: string;

  @Column({ default: 0, name: 'duration_seconds' })
  durationSeconds: number;


  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date;

  @Column({
    type: 'enum',
    enum: VideoStatus,
    default: VideoStatus.DRAFT,
  })
  status: VideoStatus;

  @Column({
    type: 'enum',
    enum: VideoType,
    default: VideoType.MOVIE,
  })
  type: VideoType;

  @Column({
    type: 'enum',
    enum: AgeRating,
    default: AgeRating.G,
    name: 'age_rating',
  })
  ageRating: AgeRating;

  @Column({ default: 'en' })
  language: string;

  @Column('text', { array: true, default: '{}', name: 'subtitle_languages' })
  subtitleLanguages: string[];

  @Column({ default: 0, name: 'view_count' })
  viewCount: number;

  @Column({ default: 0, name: 'like_count' })
  likeCount: number;

  @Column({ default: 0, name: 'comment_count' })
  commentCount: number;

  @Column({ default: 0, name: 'share_count' })
  shareCount: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0, name: 'average_rating' })
  averageRating: number;

  @Column({ default: 0, name: 'rating_count' })
  ratingCount: number;

  @Column({ nullable: true, name: 'release_date' })
  releaseDate: Date;

  @Column({ nullable: true, name: 'scheduled_publish_date' })
  scheduledPublishDate: Date;

  @Column({ default: false, name: 'is_featured' })
  isFeatured: boolean;

  @Column({ default: false, name: 'is_trending' })
  isTrending: boolean;

  @Column({ default: true, name: 'allow_comments' })
  allowComments: boolean;

  @Column({ default: true, name: 'allow_likes' })
  allowLikes: boolean;

  @Column({ default: true, name: 'allow_sharing' })
  allowSharing: boolean;

  @Column('text', { array: true, default: '{}' })
  keywords: string[];

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @Column({ name: 'uploader_id' })
  uploaderId: string;

  @Column({ nullable: true, name: 'approved_by' })
  approvedBy: string;

  @Column({ nullable: true, name: 'approved_at' })
  approvedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.uploadedVideos)
  @JoinColumn({ name: 'uploader_id' })
  uploader: User;

  @ManyToMany(() => Category, (category) => category.videos)
  @JoinTable({
    name: 'video_categories',
    joinColumn: { name: 'video_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'category_id', referencedColumnName: 'id' },
  })
  categories: Category[];

  @ManyToMany(() => Tag, (tag) => tag.videos)
  @JoinTable({
    name: 'video_tags',
    joinColumn: { name: 'video_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'tag_id', referencedColumnName: 'id' },
  })
  tags: Tag[];

  @OneToMany(() => WatchHistory, (history) => history.video)
  watchHistory: WatchHistory[];

  @OneToMany(() => Comment, (comment) => comment.video)
  comments: Comment[];

  @OneToMany(() => Like, (like) => like.video)
  likes: Like[];

  @OneToMany(() => VideoStream, (stream) => stream.video)
  streams: VideoStream[];

  // Virtual fields
  get formattedDuration(): string {
    const hours = Math.floor(this.durationSeconds / 3600);
    const minutes = Math.floor((this.durationSeconds % 3600) / 60);
    const seconds = this.durationSeconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
