import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

export enum UserEvent {
  LOGIN = 'login',
  LOGOUT = 'logout',
  VIDEO_VIEW = 'video_view',
  VIDEO_LIKE = 'video_like',
  VIDEO_SHARE = 'video_share',
  COMMENT_POST = 'comment_post',
  PLAYLIST_CREATE = 'playlist_create',
  DONATION_MADE = 'donation_made',
  PROFILE_UPDATE = 'profile_update',
  SEARCH = 'search',
  AD_CLICK = 'ad_click',
  REFERRAL = 'referral',
}

@Entity('user_analytics')
@Index(['userId', 'date'])
export class UserAnalytics {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({
    type: 'enum',
    enum: UserEvent,
  })
  event: UserEvent;

  @Column({ default: 0 })
  count: number;

  @Column({ nullable: true, name: 'video_id' })
  videoId: string;

  @Column({ nullable: true, name: 'device_type' })
  deviceType: string;

  @Column({ nullable: true })
  platform: string;

  @Column({ nullable: true })
  region: string;

  @Column({ nullable: true, name: 'ip_address' })
  ipAddress: string;

  @Column({ nullable: true, name: 'user_agent' })
  userAgent: string;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
