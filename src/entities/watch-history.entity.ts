import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { User } from './user.entity';
import { Video } from './video.entity';

@Entity()
export class WatchHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column()
  videoId: string;

  @ManyToOne(() => User, (user) => user.watchHistory)
  user: User;

  @ManyToOne(() => Video, (video) => video.watchHistory)
  video: Video;

  @Column({ default: 0 })
  watchTimeSeconds: number;

  @Column({ default: 0 })
  progressPercentage: number;

  @Column({ default: false })
  completed: boolean;

  @Column({ nullable: true })
  lastPositionSeconds: number;

  @Column({ default: 1 })
  watchCount: number;
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}