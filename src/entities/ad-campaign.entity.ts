import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Ad } from './ad.entity';
import { AdAnalytics } from './ad-analytics.entity';

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('ad_campaigns')
export class AdCampaign {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({ name: 'ad_id' })
  adId: string;

  @Column({
    type: 'enum',
    enum: CampaignStatus,
    default: CampaignStatus.DRAFT,
  })
  status: CampaignStatus;

  @Column({ name: 'start_date' })
  startDate: Date;

  @Column({ name: 'end_date' })
  endDate: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  budget: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0, name: 'spent_amount' })
  spentAmount: number;

  @Column({ default: 0, name: 'target_impressions' })
  targetImpressions: number;

  @Column({ default: 0, name: 'actual_impressions' })
  actualImpressions: number;

  @Column({ default: 0, name: 'target_clicks' })
  targetClicks: number;

  @Column({ default: 0, name: 'actual_clicks' })
  actualClicks: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, name: 'target_ctr' })
  targetCtr: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, name: 'actual_ctr' })
  actualCtr: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => Ad, (ad) => ad.campaigns)
  @JoinColumn({ name: 'ad_id' })
  ad: Ad;

  @OneToMany(() => AdAnalytics, (analytics) => analytics.campaign)
  analytics: AdAnalytics[];
}
