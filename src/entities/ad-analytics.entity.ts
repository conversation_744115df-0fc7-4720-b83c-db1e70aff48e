import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { AdCampaign } from './ad-campaign.entity';

export enum AdEvent {
  IMPRESSION = 'impression',
  CLICK = 'click',
  VIEW_START = 'view_start',
  VIEW_25 = 'view_25',
  VIEW_50 = 'view_50',
  VIEW_75 = 'view_75',
  VIEW_COMPLETE = 'view_complete',
  SKIP = 'skip',
}

@Entity('ad_analytics')
@Index(['campaignId', 'date'])
export class AdAnalytics {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'campaign_id' })
  campaignId: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({
    type: 'enum',
    enum: AdEvent,
  })
  event: AdEvent;

  @Column({ default: 0 })
  count: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  revenue: number;

  @Column({ nullable: true, name: 'user_id' })
  userId: string;

  @Column({ nullable: true, name: 'video_id' })
  videoId: string;

  @Column({ nullable: true, name: 'device_type' })
  deviceType: string;

  @Column({ nullable: true })
  region: string;

  @Column({ nullable: true, name: 'ip_address' })
  ipAddress: string;

  @Column({ nullable: true, name: 'user_agent' })
  userAgent: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => AdCampaign, (campaign) => campaign.analytics)
  @JoinColumn({ name: 'campaign_id' })
  campaign: AdCampaign;
}
