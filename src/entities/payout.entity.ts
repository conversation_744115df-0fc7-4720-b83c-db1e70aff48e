import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

export enum PayoutStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum PayoutType {
  DONATION_REVENUE = 'donation_revenue',
  AD_REVENUE = 'ad_revenue',
  BONUS = 'bonus',
  REFUND = 'refund',
}

@Entity('payouts')
export class Payout {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'recipient_id' })
  recipientId: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ default: 'USD' })
  currency: string;

  @Column({
    type: 'enum',
    enum: PayoutType,
  })
  type: PayoutType;

  @Column({
    type: 'enum',
    enum: PayoutStatus,
    default: PayoutStatus.PENDING,
  })
  status: PayoutStatus;

  @Column({ nullable: true, name: 'payment_method' })
  paymentMethod: string;

  @Column({ nullable: true, name: 'payment_provider' })
  paymentProvider: string;

  @Column({ nullable: true, name: 'transaction_id' })
  transactionId: string;

  @Column({ nullable: true, name: 'provider_transaction_id' })
  providerTransactionId: string;

  @Column({ nullable: true, name: 'bank_account' })
  bankAccount: string;

  @Column({ nullable: true, name: 'paypal_email' })
  paypalEmail: string;

  @Column('text', { nullable: true })
  notes: string;

  @Column({ nullable: true, name: 'processed_at' })
  processedAt: Date;

  @Column({ nullable: true, name: 'failed_reason' })
  failedReason: string;

  @Column({ nullable: true, name: 'processed_by' })
  processedBy: string;

  @Column('jsonb', { nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'recipient_id' })
  recipient: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'processed_by' })
  processor: User;
}
