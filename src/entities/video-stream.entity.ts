import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Video } from './video.entity';

export enum StreamQuality {
  SD_480P = '480p',
  HD_720P = '720p',
  FHD_1080P = '1080p',
  UHD_4K = '4k',
}

export enum StreamFormat {
  HLS = 'hls',
  DASH = 'dash',
  MP4 = 'mp4',
}

@Entity('video_streams')
export class VideoStream {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'video_id' })
  videoId: string;

  @Column({
    type: 'enum',
    enum: StreamQuality,
  })
  quality: StreamQuality;

  @Column({
    type: 'enum',
    enum: StreamFormat,
  })
  format: StreamFormat;

  @Column({ name: 'stream_url' })
  streamUrl: string;

  @Column({ name: 'file_size_bytes' })
  fileSizeBytes: number;

  @Column({ name: 'bitrate_kbps' })
  bitrateKbps: number;

  @Column({ default: true })
  active: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => Video, (video) => video.streams)
  @JoinColumn({ name: 'video_id' })
  video: Video;
}
