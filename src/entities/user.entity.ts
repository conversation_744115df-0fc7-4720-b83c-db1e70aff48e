import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { Video } from './video.entity';
import { Playlist } from './playlist.entity';
import { Donation } from './donation.entity';
import { UserProfile } from './user-profile.entity';
import { Role } from './role.entity';
import { Comment } from './comment.entity';
import { Like } from './like.entity';
import { UserSubscription } from './user-subscription.entity';
import { PaymentMethod } from './payment-method.entity';
import { PaymentTransaction } from './payment-transaction.entity';
import { WatchLater } from './watch-later.entity';
import { WatchHistory } from './watch-history.entity';

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BANNED = 'banned',
}

export enum AuthProvider {
  LOCAL = 'local',
  GOOGLE = 'google',
  FACEBOOK = 'facebook',
  APPLE = 'apple',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column({ nullable: true })
  @Exclude()
  password: string;

  @Column({ name: 'first_name' })
  firstName: string;

  @Column({ name: 'last_name' })
  lastName: string;

  @Column({ nullable: true })
  username: string;

  @Column({ nullable: true, name: 'phone_number' })
  phoneNumber: string;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.INACTIVE,
  })
  status: UserStatus;

  @Column({ type: 'boolean', default: false, name: 'is_approved' })
  isApproved: boolean;

  @Column({
    type: 'enum',
    enum: AuthProvider,
    default: AuthProvider.LOCAL,
    name: 'auth_provider',
  })
  authProvider: AuthProvider;

  @Column({ nullable: true, name: 'provider_id' })
  providerId: string;

  @Column({ nullable: true, name: 'google_id' })
  googleId: string;

  @Column({ nullable: true, name: 'facebook_id' })
  facebookId: string;

  @Column({ default: false, name: 'email_verified' })
  emailVerified: boolean;

  @Column({ type: 'varchar', nullable: true, name: 'email_verification_token' })
  @Exclude()
  emailVerificationToken: string | null;

  @Column({ type: 'timestamp', nullable: true, name: 'email_verification_expires' })
  emailVerificationExpires: Date | null;

  @Column({ type: 'varchar', nullable: true, name: 'password_reset_token' })
  @Exclude()
  passwordResetToken: string | null;

  @Column({ type: 'timestamp', nullable: true, name: 'password_reset_expires' })
  passwordResetExpires: Date | null;

  @Column({ default: false, name: 'two_factor_enabled' })
  twoFactorEnabled: boolean;

  @Column({ type: 'varchar', nullable: true, name: 'two_factor_secret' })
  @Exclude()
  twoFactorSecret: string;

  @Column({ nullable: true, name: 'last_login' })
  lastLogin: Date;

  @Column({ nullable: true, name: 'last_ip' })
  lastIp: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @OneToOne(() => UserProfile, (profile) => profile.user, { cascade: true })
  profile: UserProfile;

  @ManyToMany(() => Role, (role) => role.users)
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
  })
    roles: Role[];
   
   @OneToMany(() => Video, (video) => video.uploader)
  uploadedVideos: Video[];
  @OneToMany(() => WatchHistory, (history) => history.user)
  watchHistory: WatchHistory[];
  @OneToMany(() => WatchLater, (watchLater) => watchLater.user)
  watchLater: WatchLater[];
  @OneToMany(() => Playlist, (playlist) => playlist.user)
  playlists: Playlist[];
  @OneToMany(() => Donation, (donation) => donation.donor)
  donations: Donation[];
  @OneToMany(() => Comment, (comment) => comment.user)
  comments: Comment[];
  @OneToMany(() => Like, (like) => like.user)
  likes: Like[];
  @OneToMany(() => UserSubscription, (subscription) => subscription.user)
  subscriptions: UserSubscription[];
  @OneToMany(() => PaymentMethod, (paymentMethod) => paymentMethod.user)
  paymentMethods: PaymentMethod[];
  @OneToMany(() => PaymentTransaction, (transaction) => transaction.user)
  transactions: PaymentTransaction[];
  // Virtual fields
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }
  
  get hasRole(): (roleName: string) => boolean {
    return (roleName: string) => {
      return this.roles?.some((role) => role.name === roleName) || false;
    };
  }
}

    


// @Entity('users')
// export class User {
//   @PrimaryGeneratedColumn('uuid')
//   id: string;

//   @Column({ unique: true })
//   email: string;

//   @Column({ nullable: true })
//   @Exclude()
//   password: string;

//   @Column({ name: 'first_name' })
//   firstName: string;

//   @Column({ name: 'last_name' })
//   lastName: string;

//   @Column({ nullable: true })
//   username: string;

//   @Column({ nullable: true, name: 'phone_number' })
//   phoneNumber: string;

//   @Column({
//     type: 'enum',
//     enum: UserStatus,
//     default: UserStatus.INACTIVE,
//   })
//   status: UserStatus;

//   @Column({ type: 'boolean', default: false, name: 'is_approved' })
//   isApproved: boolean;

//   @Column({
//     type: 'enum',
//     enum: AuthProvider,
//     default: AuthProvider.LOCAL,
//     name: 'auth_provider',
//   })
//   authProvider: AuthProvider;

//   @Column({ nullable: true, name: 'provider_id' })
//   providerId: string;

//   @Column({ nullable: true, name: 'google_id' })
//   googleId: string;

//   @Column({ nullable: true, name: 'facebook_id' })
//   facebookId: string;

//   @Column({ default: false, name: 'email_verified' })
//   emailVerified: boolean;

//   @Column({ type: 'varchar', nullable: true, name: 'email_verification_token' })
//   @Exclude()
//   emailVerificationToken: string | null;

//   @Column({ type: 'timestamp', nullable: true, name: 'email_verification_expires' })
//   emailVerificationExpires: Date | null;

//   @Column({ type: 'varchar', nullable: true, name: 'password_reset_token' })
//   @Exclude()
//   passwordResetToken: string | null;

//   @Column({ type: 'timestamp', nullable: true, name: 'password_reset_expires' })
//   passwordResetExpires: Date | null;

//   @Column({ default: false, name: 'two_factor_enabled' })
//   twoFactorEnabled: boolean;

//   @Column({ type: 'varchar', nullable: true, name: 'two_factor_secret' })
//   @Exclude()
//   twoFactorSecret: string;

//   @Column({ nullable: true, name: 'last_login' })
//   lastLogin: Date;

//   @Column({ nullable: true, name: 'last_ip' })
//   lastIp: string;

//   @CreateDateColumn({ name: 'created_at' })
//   createdAt: Date;

//   @UpdateDateColumn({ name: 'updated_at' })
//   updatedAt: Date;

//   // Relationships
//   @OneToOne(() => UserProfile, (profile) => profile.user, { cascade: true })
//   profile: UserProfile;

//   @ManyToMany(() => Role, (role) => role.users)
//   @JoinTable({
//     name: 'user_roles',
//     joinColumn: { name: 'user_id', referencedColumnName: 'id' },
//     inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
//   })
//     roles: Role[];
    
//   }
