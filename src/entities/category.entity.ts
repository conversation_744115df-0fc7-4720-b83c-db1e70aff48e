import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Video } from './video.entity';

export enum CategoryType {
  GENRE = 'genre',
  LANGUAGE = 'language',
  AGE_GROUP = 'age_group',
  CONTENT_TYPE = 'content_type',
  THEME = 'theme',
}

@Entity('categories')
export class Category {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ unique: true })
  slug: string;

  @Column({
    type: 'enum',
    enum: CategoryType,
    default: CategoryType.GENRE,
  })
  type: CategoryType;

  @Column({ nullable: true })
  icon: string;

  @Column({ nullable: true })
  color: string;

  @Column({ nullable: true, name: 'banner_image' })
  bannerImage: string;

  @Column({ default: true })
  active: boolean;

  @Column({ default: 0, name: 'sort_order' })
  sortOrder: number;

  @Column({ nullable: true, name: 'parent_id' })
  parentId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToMany(() => Video, (video) => video.categories)
  videos: Video[];

  @ManyToOne(() => Category, (category) => category.children)
  @JoinColumn({ name: 'parent_id' })
  parent: Category;

  @OneToMany(() => Category, (category) => category.parent)
  children: Category[];
}
