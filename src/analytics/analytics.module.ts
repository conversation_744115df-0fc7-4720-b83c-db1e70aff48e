import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';

import { UserAnalytics } from '../entities/user-analytics.entity';
import { ContentAnalytics } from '../entities/content-analytics.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserAnalytics,
      ContentAnalytics,
    ]),
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService],
  exports: [AnalyticsService],
})
export class AnalyticsModule {}
