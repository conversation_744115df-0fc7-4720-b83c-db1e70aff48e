import { ExecutionContext } from '@nestjs/common';

export const ResponseWrapper = (
  message: string,
  status: boolean = true,
  data: any,
  statusCode?: number
) => {
  return {
      message,
      status,
    data,
    statusCode,
    timestamp: new Date().toISOString(),
  };
};

export const getResponseWrapper = (ctx: ExecutionContext) => {
  const response = ctx.switchToHttp().getResponse();
  return (message: string, data: any, statusCode?: number) => {
    const code = statusCode ?? response.statusCode;
    response.status(code).json(ResponseWrapper(message, code, data));
  };
};
