import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, Profile } from 'passport-facebook';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, 'facebook') {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    const clientID = configService.get<string>('FACEBOOK_APP_ID');
    const clientSecret = configService.get<string>('FACEBOOK_APP_SECRET');

    if (!clientID || !clientSecret) {
      console.warn('Facebook OAuth credentials not configured');
    }

    super({
      clientID: clientID || 'dummy',
      clientSecret: clientSecret || 'dummy',
      callbackURL: configService.get<string>('FACEBOOK_CALLBACK_URL', 'http://localhost:3000/auth/facebook/callback'),
      scope: ['email', 'public_profile'],
      profileFields: ['id', 'emails', 'name', 'picture.type(large)'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: (error: any, user?: any) => void,
  ): Promise<any> {
    try {
      const { id, name, emails, photos } = profile;
      
      const userPayload = {
        facebookId: id,
        email: emails?.[0]?.value,
        firstName: name?.givenName,
        lastName: name?.familyName,
        fullName: `${name?.givenName || ''} ${name?.familyName || ''}`.trim(),
        profilePicture: photos?.[0]?.value,
        provider: 'facebook',
        accessToken,
        refreshToken,
      };

      const user = await this.authService.validateOAuthUser(userPayload);
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  }
}
