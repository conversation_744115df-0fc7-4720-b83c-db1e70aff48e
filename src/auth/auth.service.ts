import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  InternalServerErrorException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { ConfigService } from '@nestjs/config';

import { User, UserStatus, AuthProvider } from '../entities/user.entity';
import { UserProfile, Language, Theme } from '../entities/user-profile.entity';
import { Role, RoleType } from '../entities/role.entity';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { Logger } from '@nestjs/common';
import { EmailService } from '../common/utils/email.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private userProfileRepository: Repository<UserProfile>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private emailService: EmailService,
  ) {}

  async register(registerDto: RegisterDto) {
    const { email, password, firstName, lastName, username, phoneNumber } =
      registerDto;

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: [{ email }, { username }, { phoneNumber }],
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw new ConflictException('Email already exists');
      }
      if (existingUser.username === username) {
        throw new ConflictException('Username already exists');
      }
      if (existingUser.phoneNumber === phoneNumber) {
        throw new ConflictException('Phone number already exists');
      }
    }

    // Hash password
    const saltRounds = parseInt(this.configService.get<string>('BCRYPT_ROUNDS', '12'));
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const user = this.userRepository.create({
      email,
      password: hashedPassword,
      firstName,
      lastName,
      username,
      phoneNumber,
      status: UserStatus.ACTIVE,
      authProvider: AuthProvider.LOCAL,
    });

    const savedUser = await this.userRepository.save(user);

    // Create user profile
    const profile = this.userProfileRepository.create({
      userId: savedUser.id,
      language: Language.ENGLISH,
      theme: Theme.AUTO,
    });

    await this.userProfileRepository.save(profile);

    // Assign default role
    const defaultRole = await this.roleRepository.findOne({
      where: { type: RoleType.STANDARD_USER },
    });

    if (defaultRole) {
      savedUser.roles = [defaultRole];
      await this.userRepository.save(savedUser);
    }

    // Generate JWT token
    const payload: JwtPayload = {
      sub: savedUser.id,
      email: savedUser.email,
      roles: defaultRole ? [defaultRole.name] : [],
    };

    const accessToken = this.jwtService.sign(payload);

    return {
      status: true,
      message: 'User registered successfully',
      user: this.sanitizeUser(savedUser),
      accessToken,
    };
  }
  
async registerContentCreator(registerDto: RegisterDto) {
    return this.registerUser(registerDto, RoleType.CONTENT_CREATOR);
  }

  async registerAdmin(registerDto: RegisterDto) {
    try {
      return this.registerUser(registerDto, RoleType.ADMIN);
    } catch (error) {
      this.logger.error(`Failed to register admin: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to register admin');
    }
  }

  private async registerUser(
    registerDto: RegisterDto,
    roleType: RoleType,
  ) {
    const { email, password, firstName, lastName, username, phoneNumber } =
      registerDto;
      this.logger.debug(`Registering user with email: ${email}, role: ${roleType}`);

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: [{ email }, { username }, { phoneNumber }],
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw new ConflictException('Email already exists');
      }
      if (existingUser.username === username) {
        throw new ConflictException('Username already exists');
      }
      if (existingUser.phoneNumber === phoneNumber) {
        throw new ConflictException('Phone number already exists');
      }
    }

    // Hash password
    const saltRounds = parseInt(
      this.configService.get<string>('BCRYPT_ROUNDS', '12'),
    );
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Generate email verification token
    const emailVerificationToken = Math.random().toString(36).substring(2, 8).toUpperCase();
    const emailVerificationExpires = new Date(Date.now() + 3600000); // 1 hour

    // Create user
    const user = this.userRepository.create({
      email,
      password: hashedPassword,
      firstName,
      lastName,
      username,
      phoneNumber,
      status: UserStatus.INACTIVE, // User is inactive until email is verified
      authProvider: AuthProvider.LOCAL,
      isApproved: roleType === RoleType.CONTENT_CREATOR ? false : true,
      emailVerificationToken,
      emailVerificationExpires,
    });

    const savedUser = await this.userRepository.save(user);

    // Send verification email
    await this.emailService.sendOTP(savedUser.email, emailVerificationToken);

    // Create user profile
    const profile = this.userProfileRepository.create({
      userId: savedUser.id,
      language: Language.ENGLISH,
      theme: Theme.AUTO,
    });

    await this.userProfileRepository.save(profile);

    // Assign role
    const role = await this.roleRepository.findOne({
      where: { type: roleType },
    });

    if (role) {
      savedUser.roles = [role];
      await this.userRepository.save(savedUser);
    }

    // Generate JWT token
    const payload: JwtPayload = {
      sub: savedUser.id,
      email: savedUser.email,
      roles: role ? [role.name] : [],
    };

    const accessToken = this.jwtService.sign(payload);

    return {
      status: true,
      message: 'User registered successfully',
      user: this.sanitizeUser(savedUser),
      accessToken,
    };
  }

  async login(user: User, ipAddress?: string) {
    if (!user.isApproved && user.roles.some(role => role.type === RoleType.CONTENT_CREATOR)) {
      throw new UnauthorizedException('Content Creator account is not approved yet');
    }

    // Update last login time
    this.logger.debug(`User logging in: ${user.email}`);
    
 
    if (!user.emailVerified && !user.roles.some(role => role.type === RoleType.ADMIN)) {
      // throw new UnauthorizedException('Email not verified for user account ');
      return {
        status: false,
        message: 'Email not verified. Please verify your email to login.',
        user: this.sanitizeUser(user),
      };
    }


    user.lastLogin = new Date();
    this.logger.verbose(`User ip address: ${ipAddress}`);
     if (ipAddress) {
    user.lastIp = ipAddress;
  }
    await this.userRepository.save(user);

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      roles: user.roles?.map((role) => role.name) || [],
    };

    const accessToken = this.jwtService.sign(payload);

    this.logger.debug(`Access token generated for user: ${user.email}`);
    this.logger.verbose(`User roles: ${JSON.stringify(user.roles)}`);
    this.logger.log(`User ${user.email} logged in successfully at ${user.lastLogin}`);
    // refresh token logic
     const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_REFRESH_SECRET'),
      expiresIn: '7d',
    });


    return {
      status: true,
      message: 'Login successful',
      data: this.sanitizeUser(user),
      accessToken,
      refreshToken
    };
  }

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['roles'],
    });

    if (user && (await bcrypt.compare(password, user.password))) {
      return this.sanitizeUser(user);
    }
    return null;
  }

  async validateJwtPayload(payload: JwtPayload): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: payload.sub },
      relations: ['roles', 'profile'],
    });

    if (!user || user.status !== UserStatus.ACTIVE) {
      throw new UnauthorizedException('User not found or inactive');
    }

    return user;
  }

  async refreshToken(user: User) {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      roles: user.roles?.map((role) => role.name) || [],
    };

    return {
      status: true,
      message: 'Token refreshed successfully',
      accessToken: this.jwtService.sign(payload),
    };
  }

  async validateOAuthUser(oauthData: any): Promise<any> {
    const { email, googleId, facebookId, provider } = oauthData;

    // First, try to find user by OAuth provider ID
    let user = await this.userRepository.findOne({
      where: [
        ...(googleId ? [{ googleId }] : []),
        ...(facebookId ? [{ facebookId }] : []),
        ...(email ? [{ email }] : []),
      ],
      relations: ['roles', 'profile'],
    });

    if (user) {
      // Update OAuth IDs if they don't exist
      let updated = false;
      if (googleId && !user.googleId) {
        user.googleId = googleId;
        updated = true;
      }
      if (facebookId && !user.facebookId) {
        user.facebookId = facebookId;
        updated = true;
      }

      // Update last login
      user.lastLogin = new Date();
      updated = true;

      if (updated) {
        await this.userRepository.save(user);
      }

      return this.sanitizeUser(user);
    }

    // If no user found and no email, we can't create an account
    if (!email) {
      throw new BadRequestException('Email is required for account creation');
    }

    // Create new user from OAuth data
    const authProvider = provider === 'google' ? AuthProvider.GOOGLE :
                        provider === 'facebook' ? AuthProvider.FACEBOOK :
                        AuthProvider.LOCAL;

    const newUser = this.userRepository.create({
      email,
      firstName: oauthData.firstName,
      lastName: oauthData.lastName,
      username: this.generateUsernameFromEmail(email),
      status: UserStatus.ACTIVE,
      authProvider,
      emailVerified: true, // OAuth emails are pre-verified
      lastLogin: new Date(),
      ...(googleId && { googleId }),
      ...(facebookId && { facebookId }),
    });

    const savedUser = (await this.userRepository.save(newUser)) as unknown as User;

    // Create user profile
    const profile = this.userProfileRepository.create({
      userId: savedUser.id,
      language: Language.ENGLISH,
      theme: Theme.AUTO,
      avatar: oauthData.profilePicture,
    });

    await this.userProfileRepository.save(profile);

    // Assign default role
    const defaultRole = await this.roleRepository.findOne({
      where: { type: RoleType.STANDARD_USER },
    });

    if (defaultRole) {
      savedUser.roles = [defaultRole];
      await this.userRepository.save(savedUser);
    }

    return this.sanitizeUser(savedUser);
  }

  private generateUsernameFromEmail(email: string): string {
    const baseUsername = email.split('@')[0].toLowerCase();
    const randomSuffix = Math.floor(Math.random() * 10000);
    return `${baseUsername}${randomSuffix}`;
  }

  async forgotPassword(email: string) {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      // Don't reveal if email exists
      return { message: 'If email exists, reset link has been sent' };
    }

    // Generate reset token (in production, use crypto.randomBytes)
    const resetToken = Math.random().toString(36).substring(2, 15);
    const resetExpires = new Date(Date.now() + 3600000); // 1 hour

    user.passwordResetToken = resetToken;
    user.passwordResetExpires = resetExpires;
    await this.userRepository.save(user);

    // TODO: Send email with reset link
    // await this.emailService.sendPasswordReset(user.email, resetToken);
    await this.emailService.sendEmail(user.email, 'Password Reset Request',
      `Hello ${user.firstName || ''},\n\n` +
      `You requested a password reset. Please use the following link to reset your password:\n` +
      `${this.configService.get('FRONTEND_URL')}/reset-password?token=${resetToken}&email=${encodeURIComponent(user.email)}\n\n` +
      `If you did not request this, please ignore this email.\n\n` +
      `Thank you,\nFaithStream VOD Platform Team`,`<p>Hello ${user.firstName || ''},</p>
             <p>You requested a password reset. Please use the following link to reset your password:</p>
             <p><a href="${this.configService.get('FRONTEND_URL')}/reset-password?token=${resetToken}&email=${encodeURIComponent(user.email)}">Reset Password</a></p>
             <p>If you did not request this, please ignore this email.</p>
             <p>Thank you,<br/>FaithStream VOD Platform Team</p>`);

    this.logger.log(`Password reset link sent to ${user.email}`);
    // Return success message without revealing if email exists
    return {
      status: true,
      message: 'Reset link has been sent if email exists',
   
    };

    // return {
    //   status: true,
    //   message: 'If email exists, a password reset link has been sent.',
    // };
  }

  async resetPassword(token: string, newPassword: string) {
    const user = await this.userRepository.findOne({
      where: {
        passwordResetToken: token,
      },
    });

    if (!user || !user.passwordResetExpires || user.passwordResetExpires < new Date()) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    const saltRounds = parseInt(this.configService.get<string>('BCRYPT_ROUNDS', '12'));
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    user.password = hashedPassword;
    user.passwordResetToken = null;
    user.passwordResetExpires = null;

    await this.userRepository.save(user);

    return { status: true, message: 'Password reset successfully' };
  }

  private sanitizeUser(user: User) {
    const { password, passwordResetToken, emailVerificationToken, twoFactorSecret, ...sanitized } = user;
    return sanitized;
  }

  async changePassword(userId: string, oldPassword: string, newPassword: string) {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      throw new UnauthorizedException('Old password is incorrect');
    }

    const saltRounds = parseInt(this.configService.get<string>('BCRYPT_ROUNDS', '12'));
    user.password = await bcrypt.hash(newPassword, saltRounds);

    await this.userRepository.save(user);

    return { status: true, message: 'Password changed successfully' };
  }
  async updateProfile(userId: string, profileData: Partial<UserProfile>) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (!user.profile) {
      user.profile = this.userProfileRepository.create({ userId });
    }

    Object.assign(user.profile, profileData);
    await this.userProfileRepository.save(user.profile);

    return this.sanitizeUser(user);
  }
  async getUserProfile(userId: string): Promise<UserProfile> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile'],
    });

    if (!user || !user.profile) {
      throw new UnauthorizedException('User profile not found');
    }

    return user.profile;
  }
  async updateUserProfile(
    userId: string,
    profileData: Partial<UserProfile>,
  ): Promise<UserProfile> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile'],
    });

    if (!user || !user.profile) {
      throw new UnauthorizedException('User profile not found');
    }

    Object.assign(user.profile, profileData);
    return this.userProfileRepository.save(user.profile);
  }
  async getUserRoles(userId: string): Promise<Role[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return user.roles || [];
  }
  async assignRoleToUser(userId: string, roleType: RoleType): Promise<any> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }
    const role = await this.roleRepository.findOne({ where: { type: roleType } });
    if (!role) {
      throw new BadRequestException(`Role ${roleType} not found`);
    }
    if (!user.roles) {
      user.roles = [];
    }
    if (user.roles.some((r) => r.type === roleType)) {
      throw new ConflictException(`User already has role ${roleType}`);
    }
    user.roles.push(role);
    await this.userRepository.save(user);
    return this.sanitizeUser(user);
  } 

  // check user with role content creator status if is approved

  async verifyEmail(email: string, token: string, ipAddress?: string) {
    try {
      const user = await this.userRepository.findOne({
    where: {
      email,
      emailVerificationToken: token,
      emailVerificationExpires: MoreThan(new Date()),
    },
    relations: ['roles'], // <-- This ensures roles are loaded
  });
      this.logger.log(`user found for email verification: ${JSON.stringify(user)}`);
    if (!user || user.emailVerificationToken !== token || !user.emailVerificationExpires || user.emailVerificationExpires < new Date()) {
      throw new BadRequestException('Invalid or expired verification token');
    }

    user.status = UserStatus.ACTIVE;
    user.emailVerified = true;
    user.emailVerificationToken = null;
    user.emailVerificationExpires = null;

     user.lastLogin = new Date();
    this.logger.verbose(`User ip address: ${ipAddress}`);
     if (ipAddress) {
    user.lastIp = ipAddress;
  }
    await this.userRepository.save(user);


    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      roles: user.roles.map(role => role.name),
    };

    const accessToken = this.jwtService.sign(payload);
    this.logger.log(`Access token generated for email verification: ${accessToken}`);
    this.logger.log(`User ${user.email} verified and activated successfully`);
    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_REFRESH_SECRET'),
      expiresIn: '7d',
    });



    return {
      status: true,
      message: 'User registered successfully',
      data: this.sanitizeUser(user),
      accessToken,
      refreshToken
    };
    } catch (error) {
      this.logger.error(`Error verifying email: ${error.message}`, error.stack);
      throw new InternalServerErrorException(error.message);
    }
  }
  //   async registerUnverified(registerDto: RegisterDto) {
  //   // Check if user/email exists
  //   const existing = await this.userRepository.findOne({
  //     where: [
  //       { email: registerDto.email },
  //       { username: registerDto.username }
  //     ],
  //   });
  //   if (existing) {
  //     throw new ConflictException('Email or username already exists');
  //   }

  //   // Create user with status 'pending' or 'unverified'
  //   const user = this.userRepository.create({
  //     email: registerDto.email,
  //     username: registerDto.username,
  //     password: await bcrypt.hash(registerDto.password, parseInt(this.configService.get<string>('BCRYPT_ROUNDS', '12'))),
  //     firstName: registerDto.firstName,
  //     lastName: registerDto.lastName,
  //     phoneNumber: registerDto.phoneNumber,
  //     status: UserStatus.INACTIVE,
  //     authProvider: AuthProvider.LOCAL,
  //     emailVerified: false,
  //     emailVerificationToken: null,
  //     emailVerificationExpires: null,
  //     isApproved: false, // Set to false for unverified users

  //   });
  //   await this.userRepository.save(user);

  //   // Generate email verification token (short-lived)
  //   const verificationToken = this.jwtService.sign(
  //     { email: user.email },
  //     { expiresIn: '1h', subject: user.id.toString() }
  //   );

  //   // Send verification email
  //   const subject = 'Verify your email address';
  //   const text = `Please verify your email using this token: ${verificationToken}`;
  //   const html = `<p>Please verify your email by clicking the link below:</p>
  //     <p><a href="${this.configService.get('FRONTEND_URL')}/verify-email?token=${verificationToken}&email=${encodeURIComponent(user.email)}">Verify Email</a></p>
  //     <p>If you did not request this, please ignore this email.</p>`;
  //   await this.emailService.sendEmail(user.email, subject, text, html);

  //   return {
  //     status: true,
  //     message: 'Registration successful. Please verify your email.',
  //     verificationToken, // for testing or mobile, you may omit in production
  //   };
  // }

// ...existing code...

// ...existing code...

async registerUnverified(registerDto: RegisterDto, roleType: RoleType = RoleType.STANDARD_USER) {
  const logger = new Logger('AuthService');

  // Check if user/email exists
  const existing = await this.userRepository.findOne({
    where: [
      { email: registerDto.email },
      { username: registerDto.username }
    ],
  });
  if (existing) {
    throw new ConflictException('Email or username already exists');
  }

  // Generate 6-digit verification code
  const verificationCode = randomInt(100000, 1000000).toString();

  // Set code expiry (e.g., 1 hour from now)
  const codeExpires = new Date(Date.now() + 60 * 60 * 1000);

  // Create user with status 'inactive' and store code
  let user = this.userRepository.create({
    email: registerDto.email,
    username: registerDto.username,
    password: await bcrypt.hash(
      registerDto.password,
      parseInt(this.configService.get<string>('BCRYPT_ROUNDS', '12'))
    ),
    firstName: registerDto.firstName,
    lastName: registerDto.lastName,
    phoneNumber: registerDto.phoneNumber,
    status: UserStatus.INACTIVE,
    authProvider: AuthProvider.LOCAL,
    emailVerified: false,
    emailVerificationToken: verificationCode,
    emailVerificationExpires: codeExpires,
    isApproved: false,
    
  });

  user = await this.userRepository.save(user);

  // Create user profile
  const profile = this.userProfileRepository.create({
    userId: user.id,
    language: Language.ENGLISH,
    theme: Theme.AUTO,
  });

  await this.userProfileRepository.save(profile);

  // Assign role (ManyToMany)
  const role = await this.roleRepository.findOne({
    where: { type: roleType },
  });

  if (role) {
    user.roles = [role];
    user = await this.userRepository.save(user); // Save again to update join table
    logger.log(`Assigned role "${role.type}" to user "${user.email}" (userId: ${user.id})`);
  } else {
    logger.warn(`Role "${roleType}" not found. User "${user.email}" (userId: ${user.id}) has no role assigned.`);
  }

  logger.log(`User registered successfully: ${JSON.stringify(user)}`);

  // Send verification email
  const subject = 'Verify your email address for FaithStream VOD Platform';
  const text = `Hello ${user.firstName || ''},

Your verification code for FaithStream VOD Platform is: ${verificationCode}

Enter this code in the app to verify your email address.

If you did not request this, please ignore this email.
`;

  const html = `
    <div style="background:rgb(21, 0, 62); padding: 40px 0;">
      <div style="max-width: 480px; margin: auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(120, 80, 180, 0.08); padding: 32px; font-family: 'Segoe UI', Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 24px;">
          <h2 style="color: #8f5be8; margin: 0 0 8px 0;">FaithStream VOD Platform</h2>
          <p style="color: #888; font-size: 16px; margin: 0;">Email Verification</p>
        </div>
        <p style="font-size: 16px; color: #333;">Hello${user.firstName ? ` ${user.firstName}` : ''},</p>
        <p style="font-size: 16px; color: #333;">Thank you for signing up! Please use the code below to verify your email address:</p>
        <div style="text-align: center; margin: 32px 0;">
          <span style="display: inline-block; background: #f3eaff; color: #8f5be8; font-size: 32px; letter-spacing: 8px; font-weight: bold; padding: 16px 32px; border-radius: 8px; border: 1px solid #e0d7f7;">
            ${verificationCode}
          </span>
        </div>
        <p style="font-size: 15px; color: #555;">Enter this code in the app to complete your registration.</p>
        <p style="font-size: 13px; color: #aaa; margin-top: 32px;">If you did not request this, you can safely ignore this email.</p>
        <div style="text-align: center; margin-top: 32px;">
          <span style="color: #8f5be8; font-size: 14px;">FaithStream VOD Platform Team</span>
        </div>
      </div>
    </div>
  `;

  await this.emailService.sendEmail(user.email, subject, text, html);

  return {
    status: true,
    message: 'Registration successful. Please verify your email with the code sent.',
    verificationCode, // for testing or mobile, you may omit in production
  };
}
// ...existing code...
// ...existing code...

  async verifyAndActivateUser(email: string, token: string) {
    // Verify token
    let payload: any;
    try {
      payload = this.jwtService.verify(token);
    } catch (e) {
      throw new BadRequestException('Invalid or expired token');
    }

    if (payload.email !== email) {
      throw new BadRequestException('Invalid token');
    }

    // Find user
    const user = await this.userRepository.findOne({ where: { email } });
    this.logger.debug(`Verifying user with email: ${JSON.stringify(user)}`);
    if (!user || user.status === 'active') {
      throw new BadRequestException('User not found or already verified');
    }

    // Activate user
    user.status = UserStatus.ACTIVE;
    await this.userRepository.save(user);

    // Generate access token
    const accessToken = this.jwtService.sign({
      sub: user.id,
      email: user.email,
      roles: user.roles?.map(role => role.name) || [],
    });

    return {
      status: true,
      message: 'Email verified and user activated',
      accessToken,
      user,
    };
  }
 async resendVerificationEmail(email: string) {
  const user = await this.userRepository.findOne({ where: { email } });

  if (!user || user.emailVerified) {
    throw new BadRequestException('User not found or already verified');
  }

  // Generate new 6-digit verification code
  const verificationCode = randomInt(100000, 1000000).toString();
  const codeExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour expiry

  // Update user with new code and expiry
  user.emailVerificationToken = verificationCode;
  user.emailVerificationExpires = codeExpires;
  await this.userRepository.save(user);

  // Send verification email (reuse your nice template)
  const subject = 'Verify your email address for FaithStream VOD Platform';
  const text = `Hello${user.firstName ? ` ${user.firstName}` : ''},

Your verification code for FaithStream VOD Platform is: ${verificationCode}

Enter this code in the app to verify your email address.

If you did not request this, please ignore this email.
`;

  const html = `
    <div style="background: #f6f4fa; padding: 40px 0;">
      <div style="max-width: 480px; margin: auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(120, 80, 180, 0.08); padding: 32px; font-family: 'Segoe UI', Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 24px;">
          <h2 style="color: #8f5be8; margin: 0 0 8px 0;">FaithStream VOD Platform</h2>
          <p style="color: #888; font-size: 16px; margin: 0;">Email Verification</p>
        </div>
        <p style="font-size: 16px; color: #333;">Hello${user.firstName ? ` ${user.firstName}` : ''},</p>
        <p style="font-size: 16px; color: #333;">Please use the code below to verify your email address:</p>
        <div style="text-align: center; margin: 32px 0;">
          <span style="display: inline-block; background: #f3eaff; color: #8f5be8; font-size: 32px; letter-spacing: 8px; font-weight: bold; padding: 16px 32px; border-radius: 8px; border: 1px solid #e0d7f7;">
            ${verificationCode}
          </span>
        </div>
        <p style="font-size: 15px; color: #555;">Enter this code in the app to complete your registration.</p>
        <p style="font-size: 13px; color: #aaa; margin-top: 32px;">If you did not request this, you can safely ignore this email.</p>
        <div style="text-align: center; margin-top: 32px;">
          <span style="color: #8f5be8; font-size: 14px;">FaithStream VOD Platform Team</span>
        </div>
      </div>
    </div>
  `;

  await this.emailService.sendEmail(user.email, subject, text, html);

  return {
    status: true,
    message: 'Verification email resent successfully',
  };
}
}
function randomInt(min: number, max: number): number {
  // Returns a random integer >= min and < max
  return Math.floor(Math.random() * (max - min)) + min;
}



import { MoreThan as TypeormMoreThan } from 'typeorm';

function MoreThan(date: Date): import('typeorm').FindOperator<Date> {
  return TypeormMoreThan(date);
}