import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum } from 'class-validator';
import { RoleType } from '../../entities/role.entity';

export class CreateRoleDto {
  @ApiProperty({ description: 'The name of the role' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'The type of the role', enum: RoleType })
  @IsEnum(RoleType)
  type: RoleType;

  @ApiProperty({ description: 'The description of the role' })
  @IsString()
  description: string;
}
