import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
// import { GoogleStrategy } from './strategies/google.strategy';
// import { FacebookStrategy } from './strategies/facebook.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LocalAuthGuard } from './guards/local-auth.guard';
// import { GoogleOAuthGuard } from './guards/google-oauth.guard';
// import { FacebookOAuthGuard } from './guards/facebook-oauth.guard';
import { RolesGuard } from './guards/roles.guard';

import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { UsersModule } from '../users/users.module';
import { RolesController } from './roles.controller';
import { EmailService } from '../common/utils/email.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, UserProfile, Role, Permission]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '7d'),
        },
      }),
      inject: [ConfigService],
    }),
    UsersModule,
  ],
  controllers: [AuthController, RolesController],
  providers: [
    AuthService,
    LocalStrategy,
    JwtStrategy,
    // GoogleStrategy,
    // FacebookStrategy,
    JwtAuthGuard,
    LocalAuthGuard,
    // GoogleOAuthGuard,
    // FacebookOAuthGuard,
    RolesGuard,
    EmailService,
  ],
  exports: [AuthService, JwtAuthGuard, RolesGuard],
})
export class AuthModule {}
