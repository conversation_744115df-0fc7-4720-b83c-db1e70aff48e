import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  HttpCode,
  HttpStatus,
  Req,
  Res,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { Request, Response } from 'express';

import { AuthService } from './auth.service';
import { JwtService } from '@nestjs/jwt';
import { RegisterDto } from './dto/register.dto';
import { RegisterAdminDto } from './dto/register-admin.dto';
import { RegisterContentCreatorDto } from './dto/register-content-creator.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { LoginDto } from './dto/login.dto';
import { ResendVerificationEmailDto } from './dto/resend-verification-email.dto';
import { Public } from './decorators/public.decorator';
import { CurrentUser } from './decorators/current-user.decorator';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { GoogleOAuthGuard } from './guards/google-oauth.guard';
import { FacebookOAuthGuard } from './guards/facebook-oauth.guard';
import { User } from '../entities/user.entity';
import { VerifyEmailDto } from './dto/verify-email.dto';

@ApiTags('Authentication')
@Controller('auth')
@UseGuards(ThrottlerGuard)
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly jwtService: JwtService,
  ) {}

  @Public()
  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({
    status: 201,
    description: 'User successfully registered',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'boolean' },
        message: { type: 'string' },
        user: {
          type: 'object',
          description: 'User information',
        },
        accessToken: {
          type: 'string',
          description: 'JWT access token',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'Email or username already exists' })
  async register(@Body() registerDto: RegisterDto) {
    // return this.authService.register(registerDto);
    return this.authService.registerUnverified(registerDto);
  }

   @Public()
  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify email address' })
  @ApiResponse({
    status: 200,
    description: 'Email verified successfully',
   
  })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  async verifyEmail(@Req() req: Request, @Body() verifyEmailDto: VerifyEmailDto) {
    return this.authService.verifyEmail(verifyEmailDto.email, verifyEmailDto.token, req.ip);
  }

    @Post('resend-verification-email')
  @ApiOperation({ summary: 'Resend email verification code' })
  @ApiResponse({
    status: 200,
    description: 'Verification email resent successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Verification email resent successfully' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'User not found or already verified' })
  async resendVerificationEmail(@Body() body: ResendVerificationEmailDto) {
    return this.authService.resendVerificationEmail(body.email);
  }

  @Public()
  @Post('register/content-creator')
  @ApiOperation({ summary: 'Register a new content creator' })
  @ApiResponse({
    status: 201,
    description: 'Content creator successfully registered',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'boolean' },
        message: { type: 'string' },
        user: {
          type: 'object',
          description: 'User information',
        },
        accessToken: {
          type: 'string',
          description: 'JWT access token',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({
    status: 409,
    description: 'Email, username, or phone number already exists',
  })
  async registerContentCreator(
    @Body() registerContentCreatorDto: RegisterContentCreatorDto,
  ) {
    return this.authService.registerContentCreator(registerContentCreatorDto);
  }

  @Public()
  @Post('register/admin')
  @ApiOperation({ summary: 'Register a new admin' })
  @ApiResponse({
    status: 201,
    description: 'Admin successfully registered',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'boolean' },
        message: { type: 'string' },
        user: {
          type: 'object',
          description: 'User information',
        },
        accessToken: {
          type: 'string',
          description: 'JWT access token',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({
    status: 409,
    description: 'Email, username, or phone number already exists',
  })
  async registerAdmin(@Body() registerAdminDto: RegisterAdminDto) {
    return this.authService.registerAdmin(registerAdminDto);
  }

  @Public()
  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged in',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'boolean' },
        message: { type: 'string' },
        user: {
          type: 'object',
          description: 'User information',
        },
        accessToken: {
          type: 'string',
          description: 'JWT access token',
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Req() req: Request,@Body() _loginDto: LoginDto,@CurrentUser() user: User) {
    return this.authService.login(user, req.ip);
  }


  @Public()
  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({
    status: 200,
    description: 'Password reset email sent if email exists',
  })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto.email);
  }

  @Public()
  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({
    status: 200,
    description: 'Password successfully reset',
  })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(
      resetPasswordDto.token,
      resetPasswordDto.newPassword,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({
    status: 200,
    description: 'Current user profile',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProfile(@CurrentUser() user: User) {
    return {
      status: true,
      message: 'Profile retrieved successfully',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        username: user.username,
        status: user.status,
        roles: user.roles?.map(role => role.name) || [],
        profile: user.profile,
        createdAt: user.createdAt,
      },
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('refresh')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({
    status: 200,
    description: 'New access token generated',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'boolean' },
        message: { type: 'string' },
        accessToken: {
          type: 'string',
          description: 'New JWT access token',
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async refreshToken(@CurrentUser() user: User) {
    return this.authService.refreshToken(user);
  }

  // Google OAuth endpoints
  @Public()
  @Get('google')
  @UseGuards(GoogleOAuthGuard)
  @ApiOperation({ summary: 'Initiate Google OAuth login' })
  @ApiResponse({ status: 302, description: 'Redirect to Google OAuth' })
  async googleAuth(@Req() req: Request) {
    // Guard redirects to Google
  }

  @Public()
  @Get('google/callback')
  @UseGuards(GoogleOAuthGuard)
  @ApiOperation({ summary: 'Google OAuth callback' })
  @ApiResponse({ status: 200, description: 'Google OAuth successful' })
  @ApiResponse({ status: 401, description: 'Google OAuth failed' })
  async googleAuthRedirect(@Req() req: Request, @Res() res: Response) {
    const user = req.user as any;

    if (!user) {
      return res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=Authentication failed`);
    }

    // Generate JWT token
    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.roles?.map((role: any) => role.name) || [],
    };

    const accessToken = this.jwtService.sign(payload);

    // Redirect to frontend with token
    const redirectUrl = `${process.env.FRONTEND_URL}/auth/success?token=${accessToken}`;
    return res.redirect(redirectUrl);
  }

  // Facebook OAuth endpoints
  @Public()
  @Get('facebook')
  @UseGuards(FacebookOAuthGuard)
  @ApiOperation({ summary: 'Initiate Facebook OAuth login' })
  @ApiResponse({ status: 302, description: 'Redirect to Facebook OAuth' })
  async facebookAuth(@Req() req: Request) {
    // Guard redirects to Facebook
  }

  @Public()
  @Get('facebook/callback')
  @UseGuards(FacebookOAuthGuard)
  @ApiOperation({ summary: 'Facebook OAuth callback' })
  @ApiResponse({ status: 200, description: 'Facebook OAuth successful' })
  @ApiResponse({ status: 401, description: 'Facebook OAuth failed' })
  async facebookAuthRedirect(@Req() req: Request, @Res() res: Response) {
    const user = req.user as any;

    if (!user) {
      return res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=Authentication failed`);
    }

    // Generate JWT token
    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.roles?.map((role: any) => role.name) || [],
    };

    const accessToken = this.jwtService.sign(payload);

    // Redirect to frontend with token
    const redirectUrl = `${process.env.FRONTEND_URL}/auth/success?token=${accessToken}`;
    return res.redirect(redirectUrl);
  }

  // OAuth status endpoint for mobile apps
  @Public()
  @Post('oauth/mobile')
  @ApiOperation({ summary: 'OAuth login for mobile apps' })
  @ApiResponse({ status: 200, description: 'OAuth successful' })
  @ApiResponse({ status: 401, description: 'OAuth failed' })
  async mobileOAuth(@Body() oauthData: any) {
    try {
      const user = await this.authService.validateOAuthUser(oauthData);

      // Generate JWT token
      const payload = {
        sub: user.id,
        email: user.email,
        roles: user.roles?.map((role: any) => role.name) || [],
      };

      const accessToken = this.jwtService.sign(payload);

      return {
        status: true,
        message: 'OAuth successful',
        user,
        accessToken,
      };
    } catch (error) {
      throw error;
    }
  }
}
