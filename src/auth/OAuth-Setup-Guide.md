# OAuth Setup Guide for FaithStream

This guide explains how to set up Google and Facebook OAuth authentication for the FaithStream platform.

## Environment Variables

Add these environment variables to your `.env` file:

```env
# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# Facebook OAuth
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_CALLBACK_URL=http://localhost:3000/auth/facebook/callback

# Frontend URL for redirects
FRONTEND_URL=http://localhost:3001
```

## Google OAuth Setup

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API and Google Identity API

### 2. Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in required information:
   - App name: "FaithStream"
   - User support email: your email
   - Developer contact information: your email
4. Add scopes: `email`, `profile`, `openid`
5. Add test users if in development

### 3. Create OAuth Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Add authorized redirect URIs:
   - `http://localhost:3000/auth/google/callback` (development)
   - `https://api.faithstream.com/auth/google/callback` (production)
5. Copy Client ID and Client Secret to your `.env` file

### 4. For Mobile Apps

Create additional OAuth clients:
- **Android**: Application type "Android"
- **iOS**: Application type "iOS"
- **React Native/Expo**: Use the web client ID

## Facebook OAuth Setup

### 1. Create Facebook App

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App"
3. Choose "Consumer" app type
4. Fill in app details:
   - App name: "FaithStream"
   - Contact email: your email

### 2. Configure Facebook Login

1. In your app dashboard, add "Facebook Login" product
2. Go to Facebook Login > Settings
3. Add Valid OAuth Redirect URIs:
   - `http://localhost:3000/auth/facebook/callback` (development)
   - `https://api.faithstream.com/auth/facebook/callback` (production)

### 3. Get App Credentials

1. Go to Settings > Basic
2. Copy App ID and App Secret to your `.env` file
3. Add your domain to App Domains

### 4. App Review (for production)

For production, you'll need to submit your app for review to access user data beyond test users.

## API Endpoints

### Web OAuth Flow

```
GET /auth/google          # Initiate Google OAuth
GET /auth/google/callback # Google OAuth callback

GET /auth/facebook          # Initiate Facebook OAuth
GET /auth/facebook/callback # Facebook OAuth callback
```

### Mobile OAuth

```
POST /auth/oauth/mobile
Content-Type: application/json

{
  "provider": "google", // or "facebook"
  "googleId": "user_google_id", // for Google
  "facebookId": "user_facebook_id", // for Facebook
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "profilePicture": "https://...",
  "accessToken": "oauth_access_token"
}
```

## Frontend Integration

### Web (React/Next.js)

```typescript
// Redirect to OAuth provider
const handleGoogleLogin = () => {
  window.location.href = `${API_URL}/auth/google`;
};

const handleFacebookLogin = () => {
  window.location.href = `${API_URL}/auth/facebook`;
};

// Handle OAuth callback (on success/error pages)
useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('token');
  
  if (token) {
    localStorage.setItem('authToken', token);
    router.push('/dashboard');
  }
}, []);
```

### Mobile (React Native)

```typescript
// Google Sign-In
import { GoogleSignin } from '@react-native-google-signin/google-signin';

const signInWithGoogle = async () => {
  const userInfo = await GoogleSignin.signIn();
  
  const response = await fetch(`${API_URL}/auth/oauth/mobile`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      provider: 'google',
      googleId: userInfo.user.id,
      email: userInfo.user.email,
      firstName: userInfo.user.givenName,
      lastName: userInfo.user.familyName,
      accessToken: userInfo.idToken,
    }),
  });
  
  const result = await response.json();
  // Store result.accessToken
};

// Facebook Login
import { LoginManager, AccessToken } from 'react-native-fbsdk-next';

const signInWithFacebook = async () => {
  const result = await LoginManager.logInWithPermissions(['public_profile', 'email']);
  const data = await AccessToken.getCurrentAccessToken();
  
  // Get user info from Graph API and send to backend
};
```

## Security Considerations

### 1. Token Validation

The backend validates OAuth tokens by:
- Verifying token signatures
- Checking token expiration
- Validating issuer and audience

### 2. User Data Protection

- Only request necessary scopes
- Store minimal user data
- Encrypt sensitive information
- Implement proper session management

### 3. CSRF Protection

- Use state parameter in OAuth flows
- Validate redirect URIs
- Implement proper CORS policies

## Testing

### Development Testing

1. Use test accounts provided by Google and Facebook
2. Test both successful and failed authentication flows
3. Verify user data is correctly stored and retrieved

### Test Accounts

**Google**: Use any Gmail account in development
**Facebook**: Create test users in Facebook App dashboard

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch"**
   - Ensure callback URLs match exactly in OAuth provider settings
   - Check for trailing slashes and protocol (http vs https)

2. **"invalid_client"**
   - Verify client ID and secret are correct
   - Check environment variables are loaded properly

3. **"access_denied"**
   - User cancelled OAuth flow
   - App not approved for requested scopes

4. **CORS errors**
   - Configure proper CORS settings in backend
   - Ensure frontend and backend URLs are whitelisted

### Debug Mode

Enable debug logging:

```typescript
// In development
if (process.env.NODE_ENV === 'development') {
  console.log('OAuth Debug:', {
    provider,
    userId,
    email,
    // Don't log sensitive data like tokens
  });
}
```

## Production Deployment

### 1. Update Environment Variables

```env
GOOGLE_CALLBACK_URL=https://api.faithstream.com/auth/google/callback
FACEBOOK_CALLBACK_URL=https://api.faithstream.com/auth/facebook/callback
FRONTEND_URL=https://faithstream.com
```

### 2. Update OAuth Provider Settings

- Add production callback URLs
- Update app domains
- Submit for app review if required

### 3. SSL/HTTPS

- Ensure all OAuth flows use HTTPS in production
- Configure proper SSL certificates
- Update redirect URIs to use HTTPS

### 4. Monitoring

- Monitor OAuth success/failure rates
- Track user registration sources
- Set up alerts for authentication errors

## Analytics

Track OAuth usage:

```typescript
// Track OAuth events
analytics.track('oauth_login_initiated', {
  provider: 'google',
  timestamp: new Date(),
});

analytics.track('oauth_login_completed', {
  provider: 'google',
  userId: user.id,
  isNewUser: true,
});
```

## Support

For OAuth-related issues:

1. Check provider documentation:
   - [Google OAuth 2.0](https://developers.google.com/identity/protocols/oauth2)
   - [Facebook Login](https://developers.facebook.com/docs/facebook-login/)

2. Review error logs and debug information
3. Test with different browsers and devices
4. Verify all configuration settings

## Migration from Existing Auth

If migrating from existing authentication:

1. Link existing accounts by email
2. Provide account linking flow for users
3. Maintain backward compatibility
4. Gradually migrate users to OAuth

```typescript
// Account linking example
const linkOAuthAccount = async (userId: string, oauthData: any) => {
  const user = await userRepository.findOne({ where: { id: userId } });
  
  if (oauthData.provider === 'google') {
    user.googleId = oauthData.googleId;
  } else if (oauthData.provider === 'facebook') {
    user.facebookId = oauthData.facebookId;
  }
  
  await userRepository.save(user);
};
```
