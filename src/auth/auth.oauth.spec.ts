import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';

import { AuthService } from './auth.service';
import { User, AuthProvider, UserStatus } from '../entities/user.entity';
import { UserProfile, Language, Theme } from '../entities/user-profile.entity';
import { Role, RoleType } from '../entities/role.entity';

describe('AuthService OAuth', () => {
  let service: AuthService;
  let userRepository: Repository<User>;
  let userProfileRepository: Repository<UserProfile>;
  let roleRepository: Repository<Role>;
  let jwtService: JwtService;

  const mockUserRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockUserProfileRepository = {
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockRoleRepository = {
    findOne: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(UserProfile),
          useValue: mockUserProfileRepository,
        },
        {
          provide: getRepositoryToken(Role),
          useValue: mockRoleRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    userProfileRepository = module.get<Repository<UserProfile>>(getRepositoryToken(UserProfile));
    roleRepository = module.get<Repository<Role>>(getRepositoryToken(Role));
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateOAuthUser', () => {
    const mockGoogleOAuthData = {
      provider: 'google',
      googleId: 'google123',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      profilePicture: 'https://example.com/photo.jpg',
      accessToken: 'google_access_token',
    };

    const mockFacebookOAuthData = {
      provider: 'facebook',
      facebookId: 'facebook123',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      profilePicture: 'https://example.com/photo.jpg',
      accessToken: 'facebook_access_token',
    };

    const mockRole = {
      id: 'role-id',
      name: 'Standard User',
      type: RoleType.STANDARD_USER,
    };

    it('should create new user for Google OAuth', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);
      mockUserRepository.create.mockReturnValue({
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        googleId: 'google123',
        authProvider: AuthProvider.GOOGLE,
        status: UserStatus.ACTIVE,
        emailVerified: true,
      });
      mockUserRepository.save.mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        googleId: 'google123',
        roles: [mockRole],
      });
      mockUserProfileRepository.create.mockReturnValue({});
      mockUserProfileRepository.save.mockResolvedValue({});
      mockRoleRepository.findOne.mockResolvedValue(mockRole);

      const result = await service.validateOAuthUser(mockGoogleOAuthData);

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: [
          { googleId: 'google123' },
          { email: '<EMAIL>' },
        ],
        relations: ['roles', 'profile'],
      });

      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: expect.stringContaining('test'),
        status: UserStatus.ACTIVE,
        authProvider: AuthProvider.GOOGLE,
        emailVerified: true,
        lastLogin: expect.any(Date),
        googleId: 'google123',
      });

      expect(mockUserProfileRepository.create).toHaveBeenCalledWith({
        userId: 'user-id',
        language: Language.ENGLISH,
        theme: Theme.AUTO,
        profilePicture: 'https://example.com/photo.jpg',
      });

      expect(result).toEqual({
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        googleId: 'google123',
        roles: [mockRole],
      });
    });

    it('should create new user for Facebook OAuth', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);
      mockUserRepository.create.mockReturnValue({
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        facebookId: 'facebook123',
        authProvider: AuthProvider.FACEBOOK,
        status: UserStatus.ACTIVE,
        emailVerified: true,
      });
      mockUserRepository.save.mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        facebookId: 'facebook123',
        roles: [mockRole],
      });
      mockUserProfileRepository.create.mockReturnValue({});
      mockUserProfileRepository.save.mockResolvedValue({});
      mockRoleRepository.findOne.mockResolvedValue(mockRole);

      const result = await service.validateOAuthUser(mockFacebookOAuthData);

      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: expect.stringContaining('test'),
        status: UserStatus.ACTIVE,
        authProvider: AuthProvider.FACEBOOK,
        emailVerified: true,
        lastLogin: expect.any(Date),
        facebookId: 'facebook123',
      });

      expect(result.facebookId).toBe('facebook123');
    });

    it('should update existing user with OAuth ID', async () => {
      const existingUser = {
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        googleId: null,
        facebookId: null,
        lastLogin: new Date('2023-01-01'),
        roles: [mockRole],
      };

      mockUserRepository.findOne.mockResolvedValue(existingUser);
      mockUserRepository.save.mockResolvedValue({
        ...existingUser,
        googleId: 'google123',
        lastLogin: expect.any(Date),
      });

      const result = await service.validateOAuthUser(mockGoogleOAuthData);

      expect(mockUserRepository.save).toHaveBeenCalledWith({
        ...existingUser,
        googleId: 'google123',
        lastLogin: expect.any(Date),
      });

      expect(result.googleId).toBe('google123');
    });

    it('should find existing user by OAuth ID', async () => {
      const existingUser = {
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        googleId: 'google123',
        lastLogin: new Date('2023-01-01'),
        roles: [mockRole],
      };

      mockUserRepository.findOne.mockResolvedValue(existingUser);
      mockUserRepository.save.mockResolvedValue({
        ...existingUser,
        lastLogin: expect.any(Date),
      });

      const result = await service.validateOAuthUser(mockGoogleOAuthData);

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: [
          { googleId: 'google123' },
          { email: '<EMAIL>' },
        ],
        relations: ['roles', 'profile'],
      });

      expect(result.id).toBe('user-id');
    });

    it('should throw error when no email provided for new user', async () => {
      const oauthDataWithoutEmail = {
        ...mockGoogleOAuthData,
        email: undefined,
      };

      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(service.validateOAuthUser(oauthDataWithoutEmail)).rejects.toThrow(
        'Email is required for account creation'
      );
    });

    it('should generate unique username from email', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);
      mockUserRepository.create.mockReturnValue({
        id: 'user-id',
        username: 'test1234',
      });
      mockUserRepository.save.mockResolvedValue({
        id: 'user-id',
        username: 'test1234',
        roles: [mockRole],
      });
      mockUserProfileRepository.create.mockReturnValue({});
      mockUserProfileRepository.save.mockResolvedValue({});
      mockRoleRepository.findOne.mockResolvedValue(mockRole);

      await service.validateOAuthUser(mockGoogleOAuthData);

      expect(mockUserRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          username: expect.stringMatching(/^test\d+$/),
        })
      );
    });
  });

  describe('generateUsernameFromEmail', () => {
    it('should generate username from email', () => {
      const email = '<EMAIL>';
      const username = service['generateUsernameFromEmail'](email);
      
      expect(username).toMatch(/^john\.doe\d+$/);
    });

    it('should handle email with special characters', () => {
      const email = '<EMAIL>';
      const username = service['generateUsernameFromEmail'](email);
      
      expect(username).toMatch(/^user\+test\d+$/);
    });
  });
});

// Integration test example
describe('OAuth Integration', () => {
  it('should handle complete Google OAuth flow', async () => {
    // This would be an integration test that:
    // 1. Mocks Google OAuth response
    // 2. Calls the OAuth endpoint
    // 3. Verifies user creation
    // 4. Checks JWT token generation
    // 5. Validates redirect behavior
  });

  it('should handle complete Facebook OAuth flow', async () => {
    // Similar integration test for Facebook
  });
});
