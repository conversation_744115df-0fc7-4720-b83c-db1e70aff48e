import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role, RoleType } from '../entities/role.entity';

@Injectable()
export class SeedService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
  ) {}

  async seedRoles() {
    const roles = [
      { type: RoleType.STANDARD_USER, name: 'Standard User' },
      { type: RoleType.CONTENT_CREATOR, name: 'Content Creator' },
      { type: RoleType.ADMIN, name: 'Admin' },
      { type: RoleType.SUPER_ADMIN, name: 'Super Admin' },
    ];

    for (const role of roles) {
      const existingRole = await this.roleRepository.findOne({
        where: { type: role.type },
      });

      if (!existingRole) {
        await this.roleRepository.save(this.roleRepository.create(role));
      }
    }
  }
}
