import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RevenueController } from './revenue.controller';
import { RevenueService } from './revenue.service';

import { Donation } from '../entities/donation.entity';
import { Payout } from '../entities/payout.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Donation,
      Payout,
    ]),
  ],
  controllers: [RevenueController],
  providers: [RevenueService],
  exports: [RevenueService],
})
export class RevenueModule {}
