import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AdsController } from './ads.controller';
import { AdsService } from './ads.service';

import { Ad } from '../entities/ad.entity';
import { AdCampaign } from '../entities/ad-campaign.entity';
import { AdAnalytics } from '../entities/ad-analytics.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Ad,
      AdCampaign,
      AdAnalytics,
    ]),
  ],
  controllers: [AdsController],
  providers: [AdsService],
  exports: [AdsService],
})
export class AdModule {}
