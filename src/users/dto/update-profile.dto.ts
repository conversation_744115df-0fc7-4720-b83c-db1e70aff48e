import { IsString, <PERSON><PERSON>ptional, IsBoolean, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>y, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Theme, Language } from '../../entities/user-profile.entity';

export class UpdateProfileDto {
  @ApiProperty({
    description: 'User avatar URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({
    description: 'User bio',
    example: 'Faith-based content creator and believer',
    required: false,
  })
  @IsOptional()
  @IsString()
  bio?: string;

  @ApiProperty({
    description: 'User location',
    example: 'Lagos, Nigeria',
    required: false,
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({
    description: 'User website',
    example: 'https://johndoe.com',
    required: false,
  })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiProperty({
    description: 'Date of birth',
    example: '1990-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  dateOfBirth?: Date;

  @ApiProperty({
    description: 'Gender',
    example: 'male',
    required: false,
  })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiProperty({
    description: 'UI theme preference',
    enum: Theme,
    required: false,
  })
  @IsOptional()
  @IsEnum(Theme)
  theme?: Theme;

  @ApiProperty({
    description: 'Language preference',
    enum: Language,
    required: false,
  })
  @IsOptional()
  @IsEnum(Language)
  language?: Language;

  @ApiProperty({
    description: 'Email notifications enabled',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  emailNotifications?: boolean;

  @ApiProperty({
    description: 'Push notifications enabled',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  pushNotifications?: boolean;

  @ApiProperty({
    description: 'Marketing emails enabled',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  marketingEmails?: boolean;

  @ApiProperty({
    description: 'Ad personalization enabled',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  adPersonalization?: boolean;

  @ApiProperty({
    description: 'Parental controls enabled',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  parentalControls?: boolean;

  @ApiProperty({
    description: 'Parental control PIN',
    example: '1234',
    required: false,
  })
  @IsOptional()
  @IsString()
  parentalPin?: string;

  @ApiProperty({
    description: 'Preferred content genres',
    example: ['faith-based', 'inspirational', 'family'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferredGenres?: string[];

  @ApiProperty({
    description: 'Preferred content languages',
    example: ['en', 'yo', 'ig'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferredLanguages?: string[];
}
