import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { UsersController } from './users.controller';
import { UsersService } from './users.service';

import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { WatchHistory } from '../entities/watch-history.entity';
import { Playlist } from '../entities/playlist.entity';
import { Donation } from '../entities/donation.entity';
import { Video } from '../entities/video.entity';
import { WatchLater } from '../entities/watch-later.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      UserProfile,
      Role,
      Permission,
      WatchHistory,
      Playlist,
      Donation,
      Video,
      WatchLater,
    ]),
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
