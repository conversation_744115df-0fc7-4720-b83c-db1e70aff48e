import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, ILike } from 'typeorm';
import * as bcrypt from 'bcryptjs';

import { User, UserStatus } from '../entities/user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import { Role, RoleType } from '../entities/role.entity';
import { Video } from '../entities/video.entity';
import { WatchLater } from '../entities/watch-later.entity';
import { WatchHistory } from '../entities/watch-history.entity';
import { UpdateUserDto } from './dto/update-user.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { PaginationDto } from '../common/dto/pagination.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private userProfileRepository: Repository<UserProfile>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Video)
    private videoRepository: Repository<Video>,
    @InjectRepository(WatchLater)
    private watchLaterRepository: Repository<WatchLater>,
    @InjectRepository(WatchHistory)
    private watchHistoryRepository: Repository<WatchHistory>,
  ) {}

  async findAll(paginationDto: PaginationDto, search?: string) {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const whereCondition: FindOptionsWhere<User> = {};
    
    if (search) {
      whereCondition.email = ILike(`%${search}%`);
    }

    const [users, total] = await this.userRepository.findAndCount({
      where: whereCondition,
      relations: ['profile', 'roles'],
      skip,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    return {
      data: users.map(user => this.sanitizeUser(user)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile', 'roles', 'roles.permissions'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.sanitizeUser(user);
  }

  async findByEmail(email: string) {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['profile', 'roles'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.sanitizeUser(user);
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if email or username is being changed and already exists
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });
      if (existingUser) {
        throw new ConflictException('Email already exists');
      }
    }

    if (updateUserDto.username && updateUserDto.username !== user.username) {
      const existingUser = await this.userRepository.findOne({
        where: { username: updateUserDto.username },
      });
      if (existingUser) {
        throw new ConflictException('Username already exists');
      }
    }

    Object.assign(user, updateUserDto);
    const updatedUser = await this.userRepository.save(user);

    return this.sanitizeUser(updatedUser);
  }

  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto) {
    const profile = await this.userProfileRepository.findOne({
      where: { userId },
      relations: ['user'],
    });

    if (!profile) {
      throw new NotFoundException('User profile not found');
    }

    Object.assign(profile, updateProfileDto);
    const updatedProfile = await this.userProfileRepository.save(profile);

    return updatedProfile;
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto) {
    const { currentPassword, newPassword } = changePasswordDto;

    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['id', 'password'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    const saltRounds = 12;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    await this.userRepository.update(userId, { password: hashedNewPassword });

    return { message: 'Password changed successfully' };
  }

  async assignRole(userId: string, roleId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const role = await this.roleRepository.findOne({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Check if user already has this role
    const hasRole = user.roles.some(userRole => userRole.id === roleId);
    if (hasRole) {
      throw new BadRequestException('User already has this role');
    }

    user.roles.push(role);
    await this.userRepository.save(user);

    return { message: 'Role assigned successfully' };
  }

  async removeRole(userId: string, roleId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.roles = user.roles.filter(role => role.id !== roleId);
    await this.userRepository.save(user);

    return { message: 'Role removed successfully' };
  }

  async updateStatus(userId: string, status: UserStatus) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.status = status;
    await this.userRepository.save(user);

    return { message: 'User status updated successfully' };
  }

  async approveContentCreator(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.isApproved = true;
    await this.userRepository.save(user);

    return { status:true,message: 'Content creator approved successfully' };
  }

  async getUserStats(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile', 'uploadedVideos', 'playlists', 'donations'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      totalVideosUploaded: user.uploadedVideos?.length || 0,
      totalPlaylists: user.playlists?.length || 0,
      totalDonations: user.donations?.length || 0,
      watchTimeMinutes: user.profile?.watchTimeMinutes || 0,
      videosWatched: user.profile?.videosWatched || 0,
      rewardPoints: user.profile?.rewardPoints || 0,
      referralCount: user.profile?.referralCount || 0,
    };
  }

  async deleteUser(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Soft delete by updating status
    user.status = UserStatus.BANNED;
    await this.userRepository.save(user);

    return { message: 'User deleted successfully' };
  }

    async isContentCreatorApproved(userId: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const contentCreatorRole = user.roles.find(
      (role) => role.type === RoleType.CONTENT_CREATOR,
    );

    if (!contentCreatorRole) {
      throw new UnauthorizedException('User is not a content creator');
    }

    return user.isApproved;
  }


  private sanitizeUser(user: User) {
    const { password, passwordResetToken, emailVerificationToken, twoFactorSecret, ...sanitized } = user;
    return sanitized;
  }

}
