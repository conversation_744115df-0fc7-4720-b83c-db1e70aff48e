deploy:
  stage: deploy
  only:
    refs:
      - main
      - dev
      - staging
      - production
  script:
    # Save and secure SSH key
    - echo "🔐 Writing SSH private key to file..."
    - echo "$SSH_PRIVATE_KEY" > id_rsa
    - chmod 600 id_rsa

    # Optional: Save public key
    - echo "$GIT_SSH_PUBLIC_KEY" > id_rsa.pub
    - chmod 644 id_rsa.pub

    # Start SSH connection and deployment script
    - echo "🚀 Starting remote deployment via SSH..."
    - |
      ssh -tt -i id_rsa -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP << 'EOF'
        set -e

        echo "📁 Changing to backend directory..."
        cd faithstream/backend

        echo "🔁 Switching Git remote to SSH..."
        git remote set-url origin https://<EMAIL>/atpdev2/faithstream/backend.git

        echo "🔀 Checking out branch: $CI_COMMIT_REF_NAME"
        git checkout $CI_COMMIT_REF_NAME

        # echo "💾 Stashing local changes (if any)..."
        # git stash save "Auto-stash before CI pull" || true

        echo "📥 Pulling latest code from origin/$CI_COMMIT_REF_NAME"
        git pull origin $CI_COMMIT_REF_NAME || true

        echo "📦 Installing dependencies..."
        npm install --legacy-peer-deps

        echo "🛠️ Building NestJS project..."
        npm run build

        echo "🔁 Restarting faithstream-backend service..."
        sudo systemctl restart faithstream-backend.service

        echo "📋 Verifying service status..."
        sudo systemctl status faithstream-backend.service --no-pager

        echo "✅ Deployment complete!"
        exit
      EOF

    - echo "🧹 Cleaning up SSH key..."
    - rm -f id_rsa id_rsa.pub

    - echo "✅ Done. CI/CD deployment process finished."
