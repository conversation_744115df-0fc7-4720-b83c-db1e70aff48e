# =================================
# FaithStream VOD Platform Backend
# Environment Configuration
# =================================

# Application Configuration
NODE_ENV=development
PORT=3090
APP_NAME=FaithStream VOD Platform
APP_VERSION=1.0.0
APP_URL=http://localhost:3090

# Database Configuration
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=faithstream_user
DB_PASSWORD=your_secure_password_here
DB_DATABASE=faithstream_db
DB_SYNCHRONIZE=false
DB_LOGGING=false
DB_SSL=false

# Test Database (for running tests)
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_USERNAME=faithstream_test_user
TEST_DB_PASSWORD=your_test_password_here
TEST_DB_DATABASE=faithstream_test_db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-change-this-too
JWT_REFRESH_EXPIRES_IN=30d

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100
RATE_LIMIT_AUTH_TTL=60
RATE_LIMIT_AUTH_LIMIT=10

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# File Upload Configuration
MAX_FILE_SIZE=100MB
UPLOAD_PATH=./uploads
ALLOWED_VIDEO_FORMATS=mp4,avi,mov,mkv,webm
ALLOWED_IMAGE_FORMATS=jpg,jpeg,png,gif,webp
ALLOWED_SUBTITLE_FORMATS=srt,vtt,ass

# CDN Configuration (Optional)
CDN_URL=https://cdn.faithstream.com
CDN_ENABLED=false

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM_NAME=FaithStream Platform
SMTP_FROM_EMAIL=<EMAIL>

# Email Templates
EMAIL_VERIFICATION_TEMPLATE=email-verification
PASSWORD_RESET_TEMPLATE=password-reset
WELCOME_EMAIL_TEMPLATE=welcome

# Redis Configuration (for caching and sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TTL=3600

# Session Configuration
SESSION_SECRET=your-session-secret-change-this
SESSION_MAX_AGE=86400000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs
LOG_MAX_FILES=10
LOG_MAX_SIZE=10m

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=60000

# Payment Configuration (Optional)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# Social Media Integration (Optional)
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Video Processing Configuration
VIDEO_PROCESSING_ENABLED=true
VIDEO_QUALITIES=480p,720p,1080p
VIDEO_FORMATS=hls,dash
THUMBNAIL_SIZES=small:320x180,medium:640x360,large:1280x720

# Search Configuration
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_INDEX=faithstream
SEARCH_ENABLED=false

# Monitoring and Health Checks
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# External Services
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=faithstream-uploads
AWS_CLOUDFRONT_DOMAIN=your-cloudfront-domain.cloudfront.net

# Google Cloud Configuration (Alternative to AWS)
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_STORAGE_BUCKET=faithstream-uploads
GOOGLE_CLOUD_CDN_DOMAIN=your-cdn-domain.com

# Content Moderation
CONTENT_MODERATION_ENABLED=true
AUTO_MODERATION_ENABLED=false
MODERATION_API_KEY=your_moderation_api_key

# Notification Configuration
PUSH_NOTIFICATIONS_ENABLED=true
FCM_SERVER_KEY=your_fcm_server_key
APNS_KEY_ID=your_apns_key_id
APNS_TEAM_ID=your_apns_team_id

# API Rate Limiting
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=100
API_RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# Development Tools
SWAGGER_ENABLED=true
SWAGGER_PATH=api/docs
DEBUG_MODE=false
PROFILING_ENABLED=false

# Feature Flags
FEATURE_DONATIONS_ENABLED=true
FEATURE_LIVE_STREAMING_ENABLED=false
FEATURE_PREMIUM_SUBSCRIPTIONS_ENABLED=false
FEATURE_OFFLINE_DOWNLOADS_ENABLED=false
FEATURE_SOCIAL_LOGIN_ENABLED=true
FEATURE_TWO_FACTOR_AUTH_ENABLED=true

# Localization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,pt
TIMEZONE=UTC

# Content Delivery
STREAMING_PROTOCOL=hls
ADAPTIVE_BITRATE_ENABLED=true
PRELOAD_STRATEGY=metadata
BUFFER_SIZE=30

# Security Headers
HELMET_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
REFERRER_POLICY=strict-origin-when-cross-origin

# Database Connection Pool
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000

# Caching Strategy
CACHE_TTL_SHORT=300
CACHE_TTL_MEDIUM=1800
CACHE_TTL_LONG=3600
CACHE_TTL_VERY_LONG=86400

# Error Tracking (Optional)
SENTRY_DSN=your_sentry_dsn
SENTRY_ENVIRONMENT=development
SENTRY_RELEASE=1.0.0

# Performance Monitoring
NEW_RELIC_LICENSE_KEY=your_new_relic_license_key
NEW_RELIC_APP_NAME=FaithStream Backend

# =================================
# Production Notes:
# =================================
# 1. Change all default passwords and secrets
# 2. Set NODE_ENV=production
# 3. Enable SSL for database connections
# 4. Configure proper CORS origins
# 5. Set up proper email SMTP settings
# 6. Configure CDN for file delivery
# 7. Enable Redis for caching
# 8. Set up monitoring and logging
# 9. Configure backup strategies
# 10. Review and enable security features
