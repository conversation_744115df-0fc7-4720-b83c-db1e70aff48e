# FaithStream VOD Platform API Documentation

## Overview
The FaithStream VOD Platform API provides comprehensive endpoints for managing faith-based video content, user authentication, streaming, monetization, and analytics.

**Base URL:** `http://localhost:3090/api/v1`
**API Documentation:** `http://localhost:3090/api/docs` (Swagger UI)

## Authentication
The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Quick Start

### 1. Import Postman Collection
Import the `FaithStream-API-Collection.json` file into Postman to get started quickly with all endpoints.

### 2. Admin Login
```bash
curl -X POST http://localhost:3090/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!"
  }'
```

### 3. Get Categories
```bash
curl -X GET http://localhost:3090/api/v1/categories
```

## API Endpoints Overview

### Authentication Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/auth/register` | Register new user | No |
| POST | `/auth/login` | User login | No |
| GET | `/auth/profile` | Get current user profile | Yes |
| POST | `/auth/forgot-password` | Request password reset | No |
| POST | `/auth/reset-password` | Reset password with token | No |
| POST | `/auth/refresh` | Refresh JWT token | Yes |

### User Management Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/users` | Get all users (admin) | Yes |
| GET | `/users/me` | Get current user | Yes |
| GET | `/users/me/stats` | Get user statistics | Yes |
| GET | `/users/:id` | Get user by ID | Yes |
| PUT | `/users/me` | Update current user | Yes |
| PUT | `/users/me/profile` | Update user profile | Yes |
| PUT | `/users/me/password` | Change password | Yes |
| PUT | `/users/:id` | Update user (admin) | Yes |
| PUT | `/users/:id/role/:roleId` | Assign role to user | Yes |
| DELETE | `/users/:id/role/:roleId` | Remove role from user | Yes |
| PUT | `/users/:id/status` | Update user status | Yes |
| DELETE | `/users/:id` | Delete user | Yes |

### Content Management Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/content` | Get all videos | No |
| GET | `/content/featured` | Get featured videos | No |
| GET | `/content/trending` | Get trending videos | No |
| GET | `/content/recommended` | Get recommended videos | Yes |
| GET | `/content/search` | Search videos | No |
| GET | `/content/my-videos` | Get user's videos | Yes |
| GET | `/content/admin` | Get all videos (admin) | Yes |
| GET | `/content/:id` | Get video by ID | No |
| POST | `/content` | Create new video | Yes |
| POST | `/content/upload` | Upload video file | Yes |
| PUT | `/content/:id` | Update video | Yes |
| PUT | `/content/:id/status` | Update video status | Yes |
| PUT | `/content/:id/featured` | Toggle featured status | Yes |
| DELETE | `/content/:id` | Delete video | Yes |

### Categories Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/categories` | Get all categories | No |
| GET | `/categories/hierarchy` | Get category hierarchy | No |
| GET | `/categories/popular` | Get popular categories | No |
| GET | `/categories/slug/:slug` | Get category by slug | No |
| GET | `/categories/:id` | Get category by ID | No |
| POST | `/categories` | Create category | Yes |
| PUT | `/categories/:id` | Update category | Yes |
| DELETE | `/categories/:id` | Delete category | Yes |

### Tags Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/tags` | Get all tags | No |
| GET | `/tags/popular` | Get popular tags | No |
| GET | `/tags/cloud` | Get tag cloud | No |
| GET | `/tags/search` | Search tags | No |
| GET | `/tags/slug/:slug` | Get tag by slug | No |
| GET | `/tags/:id` | Get tag by ID | No |
| POST | `/tags` | Create tag | Yes |
| PUT | `/tags/:id` | Update tag | Yes |
| DELETE | `/tags/:id` | Delete tag | Yes |

### Streaming Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/streaming/videos/:id/streams` | Get video streams | Yes |
| PUT | `/streaming/videos/:id/progress` | Update watch progress | Yes |
| GET | `/streaming/watch-history` | Get watch history | Yes |
| GET | `/streaming/continue-watching` | Get continue watching | Yes |
| GET | `/streaming/videos/:id/comments` | Get video comments | No |
| POST | `/streaming/videos/:id/comments` | Post comment | Yes |
| POST | `/streaming/videos/:id/like` | Like video | Yes |
| POST | `/streaming/videos/:id/dislike` | Dislike video | Yes |
| POST | `/streaming/videos/:id/share` | Share video | Yes |

### Playlists Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/playlists` | Get user playlists | Yes |
| POST | `/playlists` | Create playlist | Yes |
| GET | `/playlists/:id` | Get playlist by ID | Yes |
| PUT | `/playlists/:id` | Update playlist | Yes |
| DELETE | `/playlists/:id` | Delete playlist | Yes |
| POST | `/playlists/:id/videos` | Add video to playlist | Yes |
| DELETE | `/playlists/:id/videos/:videoId` | Remove video from playlist | Yes |

### Advertisements Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/ads` | Get advertisements | Yes |
| POST | `/ads` | Create advertisement | Yes |
| GET | `/ads/:id` | Get ad by ID | Yes |
| PUT | `/ads/:id` | Update advertisement | Yes |
| DELETE | `/ads/:id` | Delete advertisement | Yes |
| PUT | `/ads/:id/status` | Update ad status | Yes |

### Revenue & Donations Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/revenue` | Get revenue overview | Yes |
| GET | `/revenue/donations` | Get donations | Yes |
| POST | `/revenue/donations` | Create donation | Yes |
| GET | `/revenue/payouts` | Get payouts | Yes |
| POST | `/revenue/payouts` | Create payout | Yes |

### Analytics Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/analytics` | Get analytics dashboard | Yes |
| GET | `/analytics/content` | Get content analytics | Yes |
| GET | `/analytics/users` | Get user analytics | Yes |
| GET | `/analytics/revenue` | Get revenue analytics | Yes |

### Admin Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/admin/dashboard` | Get admin dashboard | Yes (Admin) |
| GET | `/admin/users` | Get all users | Yes (Admin) |
| GET | `/admin/content` | Get all content | Yes (Admin) |
| GET | `/admin/reports` | Get system reports | Yes (Admin) |
| POST | `/admin/bulk-actions` | Perform bulk actions | Yes (Admin) |

## Sample Requests and Responses

### User Registration
**Request:**
```json
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe",
  "username": "johndoe",
  "phoneNumber": "+**********"
}
```

**Response:**
```json
{
  "user": {
    "id": "7f91364d-3803-4e3a-ae70-f3ddcba25b0f",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "username": "johndoe",
    "phoneNumber": "+**********",
    "status": "active",
    "authProvider": "local",
    "emailVerified": false,
    "twoFactorEnabled": false,
    "createdAt": "2025-06-06T00:06:15.690Z",
    "updatedAt": "2025-06-06T00:06:15.690Z",
    "roles": [
      {
        "id": "f2d1d054-5318-4a37-aa77-ee5e0364a3c9",
        "name": "standard_user",
        "description": "Regular platform user",
        "type": "standard_user"
      }
    ]
  },
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### User Login
**Request:**
```json
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin123!"
}
```

**Response:**
```json
{
  "user": {
    "id": "985de753-16fd-4bc7-849f-f094e9401ed9",
    "email": "<EMAIL>",
    "firstName": "Super",
    "lastName": "Admin",
    "username": "superadmin",
    "status": "active",
    "emailVerified": true,
    "lastLogin": "2025-06-06T00:07:33.266Z",
    "profile": {
      "id": "4df8f9b4-1c85-4a57-8332-9b4e0fba07fb",
      "theme": "auto",
      "language": "en",
      "watchTimeMinutes": 0,
      "videosWatched": 0
    },
    "roles": [
      {
        "id": "c9384bf1-8ce4-4d93-8fdd-14d7ecc3ef49",
        "name": "super_admin",
        "description": "Full system access",
        "type": "super_admin"
      }
    ]
  },
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Get Categories
**Request:**
```bash
GET /api/v1/categories
```

**Response:**
```json
[
  {
    "id": "261d9c43-b77c-413d-a953-188dcc186d10",
    "name": "Faith-Based",
    "description": null,
    "slug": "faith-based",
    "type": "genre",
    "icon": "fa-cross",
    "color": "#4A90E2",
    "bannerImage": null,
    "active": true,
    "sortOrder": 0,
    "parentId": null,
    "createdAt": "2025-06-05T23:45:27.101Z",
    "updatedAt": "2025-06-05T23:45:27.101Z",
    "parent": null,
    "children": []
  }
]
```

### Create Video Content
**Request:**
```json
POST /api/v1/content
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "New Faith Documentary",
  "description": "A powerful documentary about faith in modern times",
  "videoUrl": "https://example.com/new-video.mp4",
  "thumbnail": "https://example.com/new-thumbnail.jpg",
  "duration": 5400,
  "type": "documentary",
  "ageRating": "PG",
  "language": "en",
  "categoryIds": ["category-uuid-1"],
  "tagIds": ["tag-uuid-1"]
}
```

**Response:**
```json
{
  "id": "new-video-uuid",
  "title": "New Faith Documentary",
  "description": "A powerful documentary about faith in modern times",
  "status": "draft",
  "type": "documentary",
  "createdAt": "2025-06-06T00:15:00.000Z",
  "message": "Video created successfully"
}
```

### Update Watch Progress
**Request:**
```json
PUT /api/v1/streaming/videos/{videoId}/progress
Authorization: Bearer <token>
Content-Type: application/json

{
  "watchTimeSeconds": 1800,
  "progressPercentage": 25,
  "lastPositionSeconds": 1800,
  "completed": false,
  "quality": "1080p",
  "deviceType": "desktop"
}
```

**Response:**
```json
{
  "message": "Watch progress updated successfully",
  "watchHistory": {
    "id": "watch-history-uuid",
    "watchTimeSeconds": 1800,
    "progressPercentage": 25,
    "lastPositionSeconds": 1800,
    "completed": false,
    "watchCount": 1,
    "updatedAt": "2025-06-06T00:25:00.000Z"
  }
}
```

## Error Responses

All API endpoints return consistent error responses:

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "email",
      "message": "Email must be a valid email address"
    }
  ]
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

The API implements rate limiting:
- **General endpoints:** 100 requests per minute
- **Authentication endpoints:** 10 requests per minute
- **Upload endpoints:** 5 requests per minute

## Pagination

List endpoints support pagination with query parameters:
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10, max: 100)

Response includes pagination metadata:
```json
{
  "data": [...],
  "total": 150,
  "page": 1,
  "limit": 10,
  "totalPages": 15
}
```

## Testing with Postman

1. Import the `FaithStream-API-Collection.json` file
2. Set the `baseUrl` variable to `http://localhost:3090/api/v1`
3. Login with admin credentials to get an access token
4. The token will be automatically set for authenticated requests
5. Test all endpoints with sample data

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: http://localhost:3090/api/docs
