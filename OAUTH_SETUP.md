# OAuth Setup Guide for FaithStream

This guide will help you set up Google and Facebook OAuth authentication for your FaithStream application.

## 🔧 Environment Variables

Add these to your `.env` file (samples already included):

```env
# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_CALLBACK_URL=http://localhost:3090/auth/google/callback

# Facebook OAuth
FACEBOOK_APP_ID=your_facebook_app_id_here
FACEBOOK_APP_SECRET=your_facebook_app_secret_here
FACEBOOK_CALLBACK_URL=http://localhost:3090/auth/facebook/callback

# Frontend URL
FRONTEND_URL=http://localhost:3000
```

## 🔵 Google OAuth Setup

### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: "FaithStream"
4. Click "Create"

### Step 2: Enable APIs
1. Go to "APIs & Services" → "Library"
2. Search for "Google+ API" and enable it
3. Search for "Google Identity API" and enable it

### Step 3: Configure OAuth Consent Screen
1. Go to "APIs & Services" → "OAuth consent screen"
2. Choose "External" user type
3. Fill in required information:
   - **App name**: FaithStream
   - **User support email**: <EMAIL>
   - **Developer contact**: <EMAIL>
4. Add scopes: `email`, `profile`, `openid`
5. Save and continue

### Step 4: Create OAuth Credentials
1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. **Name**: FaithStream Web Client
5. **Authorized redirect URIs**:
   - `http://localhost:3090/auth/google/callback`
   - `https://yourdomain.com/auth/google/callback` (for production)
6. Click "Create"
7. Copy the **Client ID** and **Client Secret** to your `.env` file

### Example:
```env
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz
```

## 🔵 Facebook OAuth Setup

### Step 1: Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App"
3. Choose "Consumer" app type
4. **App name**: FaithStream
5. **Contact email**: <EMAIL>
6. Click "Create App"

### Step 2: Add Facebook Login Product
1. In your app dashboard, click "Add Product"
2. Find "Facebook Login" and click "Set Up"
3. Choose "Web" platform
4. Enter your site URL: `http://localhost:3090`

### Step 3: Configure Facebook Login Settings
1. Go to "Facebook Login" → "Settings"
2. **Valid OAuth Redirect URIs**:
   - `http://localhost:3090/auth/facebook/callback`
   - `https://yourdomain.com/auth/facebook/callback` (for production)
3. Save changes

### Step 4: Get App Credentials
1. Go to "Settings" → "Basic"
2. Copy **App ID** and **App Secret** to your `.env` file
3. Add your domain to **App Domains**: `localhost` (for development)

### Example:
```env
FACEBOOK_APP_ID=1234567890123456
FACEBOOK_APP_SECRET=abcdefghijklmnopqrstuvwxyz123456
```

## 🧪 Testing OAuth

### 1. Start the Application
```bash
npm run start:dev
```

### 2. Test Google OAuth
Open in browser: `http://localhost:3090/auth/google`

### 3. Test Facebook OAuth
Open in browser: `http://localhost:3090/auth/facebook`

### 4. API Endpoints Available
```
GET  /auth/google                 # Initiate Google OAuth
GET  /auth/google/callback        # Google OAuth callback
GET  /auth/facebook               # Initiate Facebook OAuth
GET  /auth/facebook/callback      # Facebook OAuth callback
POST /auth/oauth/mobile           # Mobile OAuth (React Native)
```

## 🔧 Frontend Integration Examples

### React/Next.js
```typescript
const handleGoogleLogin = () => {
  window.location.href = 'http://localhost:3090/auth/google';
};

const handleFacebookLogin = () => {
  window.location.href = 'http://localhost:3090/auth/facebook';
};
```

### React Native
```typescript
import { GoogleSignin } from '@react-native-google-signin/google-signin';

const signInWithGoogle = async () => {
  const userInfo = await GoogleSignin.signIn();
  
  const response = await fetch('http://localhost:3090/auth/oauth/mobile', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      provider: 'google',
      googleId: userInfo.user.id,
      email: userInfo.user.email,
      firstName: userInfo.user.givenName,
      lastName: userInfo.user.familyName,
      accessToken: userInfo.idToken,
    }),
  });
  
  const result = await response.json();
  // Store result.accessToken
};
```

## 🚨 Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch"**
   - Check callback URLs match exactly in OAuth provider settings
   - Ensure no trailing slashes

2. **"invalid_client"**
   - Verify client ID and secret are correct
   - Check environment variables are loaded

3. **CORS errors**
   - Update CORS_ORIGIN in .env file
   - Ensure frontend URL is whitelisted

### Debug Mode:
Set `NODE_ENV=development` to see detailed OAuth logs.

## 🚀 Production Setup

### Update Environment Variables:
```env
GOOGLE_CALLBACK_URL=https://api.yourdomain.com/auth/google/callback
FACEBOOK_CALLBACK_URL=https://api.yourdomain.com/auth/facebook/callback
FRONTEND_URL=https://yourdomain.com
```

### Update OAuth Provider Settings:
1. Add production callback URLs to Google Cloud Console
2. Add production redirect URIs to Facebook App settings
3. Update app domains in both platforms

## 📱 Mobile App Configuration

### Google (React Native):
```typescript
GoogleSignin.configure({
  webClientId: 'your_google_client_id_here',
  offlineAccess: true,
});
```

### Facebook (React Native):
```typescript
Settings.setAppID('your_facebook_app_id_here');
```

## ✅ Verification Checklist

- [ ] Google Cloud project created
- [ ] Google OAuth credentials configured
- [ ] Facebook app created
- [ ] Facebook Login product added
- [ ] Environment variables updated
- [ ] Callback URLs configured
- [ ] Test authentication flows working
- [ ] Frontend integration complete

## 🔗 Useful Links

- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Facebook Login Documentation](https://developers.facebook.com/docs/facebook-login/)
- [React Native Google Sign-In](https://github.com/react-native-google-signin/google-signin)
- [React Native Facebook SDK](https://github.com/thebergamo/react-native-fbsdk-next)

---

**Need Help?** Check the OAuth-Setup-Guide.md file in the src/auth/ directory for more detailed technical information.
