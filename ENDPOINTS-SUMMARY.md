# FaithStream VOD Platform - Complete API Endpoints Summary

## 🚀 Quick Access
- **API Base URL:** `http://localhost:3090/api/v1`
- **Swagger Documentation:** `http://localhost:3090/api/docs`
- **Postman Collection:** `postman/FaithStream-API-Collection.json`
- **Test Script:** `./test-api.sh`

## 🔐 Authentication Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| POST | `/auth/register` | Register new user | `{"email":"<EMAIL>","password":"Pass123!","firstName":"John","lastName":"Doe","username":"johndo<PERSON>"}` |
| POST | `/auth/login` | User login | `{"email":"<EMAIL>","password":"Admin123!"}` |
| GET | `/auth/profile` | Get current user profile | - |
| POST | `/auth/forgot-password` | Request password reset | `{"email":"<EMAIL>"}` |
| POST | `/auth/reset-password` | Reset password with token | `{"token":"reset-token","newPassword":"NewPass123!"}` |
| POST | `/auth/refresh` | Refresh JWT token | - |
| POST | `/auth/logout` | Logout user | - |

## 👥 User Management Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/users` | Get all users (admin) | - |
| GET | `/users/me` | Get current user | - |
| GET | `/users/me/stats` | Get user statistics | - |
| GET | `/users/:id` | Get user by ID | - |
| PUT | `/users/me` | Update current user | `{"firstName":"John","lastName":"Doe","phoneNumber":"+1234567890"}` |
| PUT | `/users/me/profile` | Update user profile | `{"theme":"dark","language":"en","notifications":true}` |
| PUT | `/users/me/password` | Change password | `{"currentPassword":"Old123!","newPassword":"New123!"}` |
| PUT | `/users/:id` | Update user (admin) | `{"status":"active","emailVerified":true}` |
| PUT | `/users/:id/role/:roleId` | Assign role to user | - |
| DELETE | `/users/:id/role/:roleId` | Remove role from user | - |
| PUT | `/users/:id/status` | Update user status | `{"status":"suspended","reason":"Policy violation"}` |
| DELETE | `/users/:id` | Delete user | - |

## 🎬 Content Management Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/content` | Get all videos | Query: `?page=1&limit=10&status=published&category=faith-based` |
| GET | `/content/featured` | Get featured videos | Query: `?limit=5` |
| GET | `/content/trending` | Get trending videos | Query: `?limit=10&timeframe=week` |
| GET | `/content/recommended` | Get recommended videos | Query: `?limit=10` |
| GET | `/content/search` | Search videos | Query: `?q=faith&category=inspirational&language=en` |
| GET | `/content/my-videos` | Get user's videos | Query: `?page=1&limit=10` |
| GET | `/content/admin` | Get all videos (admin) | Query: `?status=pending&page=1` |
| GET | `/content/:id` | Get video by ID | - |
| POST | `/content` | Create new video | `{"title":"Faith Journey","description":"Inspiring story","videoUrl":"https://example.com/video.mp4","duration":7200,"type":"movie","ageRating":"PG","language":"en"}` |
| POST | `/content/upload` | Upload video file | FormData: `video`, `thumbnail` |
| PUT | `/content/:id` | Update video | `{"title":"Updated Title","description":"Updated description"}` |
| PUT | `/content/:id/status` | Update video status | `{"status":"published","reviewNotes":"Approved"}` |
| PUT | `/content/:id/featured` | Toggle featured status | `{"featured":true}` |
| DELETE | `/content/:id` | Delete video | - |

## 📂 Categories Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/categories` | Get all categories | Query: `?type=genre&active=true` |
| GET | `/categories/hierarchy` | Get category hierarchy | - |
| GET | `/categories/popular` | Get popular categories | Query: `?limit=5` |
| GET | `/categories/slug/:slug` | Get category by slug | - |
| GET | `/categories/:id` | Get category by ID | - |
| POST | `/categories` | Create category | `{"name":"Worship","description":"Worship content","type":"genre","icon":"fa-music","color":"#9B59B6"}` |
| PUT | `/categories/:id` | Update category | `{"name":"Updated Name","description":"Updated description"}` |
| DELETE | `/categories/:id` | Delete category | - |

## 🏷️ Tags Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/tags` | Get all tags | Query: `?search=faith&limit=20` |
| GET | `/tags/popular` | Get popular tags | Query: `?limit=10` |
| GET | `/tags/cloud` | Get tag cloud | Query: `?limit=50` |
| GET | `/tags/search` | Search tags | Query: `?q=faith` |
| GET | `/tags/slug/:slug` | Get tag by slug | - |
| GET | `/tags/:id` | Get tag by ID | - |
| POST | `/tags` | Create tag | `{"name":"prayer","description":"Content related to prayer"}` |
| PUT | `/tags/:id` | Update tag | `{"name":"Updated Tag","description":"Updated description"}` |
| DELETE | `/tags/:id` | Delete tag | - |

## 📺 Streaming Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/streaming/videos/:id/streams` | Get video streams | - |
| PUT | `/streaming/videos/:id/progress` | Update watch progress | `{"watchTimeSeconds":1800,"progressPercentage":25,"lastPositionSeconds":1800,"completed":false,"quality":"1080p","deviceType":"desktop"}` |
| GET | `/streaming/watch-history` | Get watch history | Query: `?page=1&limit=20` |
| GET | `/streaming/continue-watching` | Get continue watching | Query: `?limit=10` |
| GET | `/streaming/videos/:id/comments` | Get video comments | Query: `?page=1&limit=20&sort=newest` |
| POST | `/streaming/videos/:id/comments` | Post comment | `{"content":"Amazing video!","parentId":null}` |
| PUT | `/streaming/comments/:id` | Update comment | `{"content":"Updated comment"}` |
| DELETE | `/streaming/comments/:id` | Delete comment | - |
| POST | `/streaming/videos/:id/like` | Like video | - |
| POST | `/streaming/videos/:id/dislike` | Dislike video | - |
| DELETE | `/streaming/videos/:id/like` | Remove like | - |
| POST | `/streaming/videos/:id/share` | Share video | `{"platform":"facebook","message":"Check this out!"}` |
| POST | `/streaming/videos/:id/report` | Report video | `{"reason":"inappropriate","description":"Details"}` |

## 📋 Playlists Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/playlists` | Get user playlists | Query: `?page=1&limit=10` |
| POST | `/playlists` | Create playlist | `{"name":"My Faith Videos","description":"Collection of inspiring videos","isPublic":true}` |
| GET | `/playlists/:id` | Get playlist by ID | - |
| PUT | `/playlists/:id` | Update playlist | `{"name":"Updated Name","description":"Updated description"}` |
| DELETE | `/playlists/:id` | Delete playlist | - |
| POST | `/playlists/:id/videos` | Add video to playlist | `{"videoId":"video-uuid","position":1}` |
| DELETE | `/playlists/:id/videos/:videoId` | Remove video from playlist | - |
| PUT | `/playlists/:id/videos/reorder` | Reorder playlist videos | `{"videoIds":["uuid1","uuid2","uuid3"]}` |

## 📢 Advertisements Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/ads` | Get advertisements | Query: `?status=active&type=video` |
| POST | `/ads` | Create advertisement | `{"title":"Faith App Ad","description":"Download our app","type":"video","targetAudience":"all","budget":1000,"startDate":"2025-06-01","endDate":"2025-06-30"}` |
| GET | `/ads/:id` | Get ad by ID | - |
| PUT | `/ads/:id` | Update advertisement | `{"title":"Updated Ad","budget":1500}` |
| DELETE | `/ads/:id` | Delete advertisement | - |
| PUT | `/ads/:id/status` | Update ad status | `{"status":"paused","reason":"Budget review"}` |
| GET | `/ads/:id/analytics` | Get ad analytics | Query: `?startDate=2025-06-01&endDate=2025-06-30` |

## 💰 Revenue & Donations Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/revenue` | Get revenue overview | Query: `?period=month&year=2025&month=6` |
| GET | `/revenue/donations` | Get donations | Query: `?page=1&limit=20&status=completed` |
| POST | `/revenue/donations` | Create donation | `{"amount":50.00,"currency":"USD","recipientId":"creator-uuid","message":"Keep up the great work!"}` |
| GET | `/revenue/payouts` | Get payouts | Query: `?page=1&limit=20&status=pending` |
| POST | `/revenue/payouts` | Create payout | `{"amount":100.00,"currency":"USD","recipientId":"creator-uuid","method":"bank_transfer"}` |
| GET | `/revenue/transactions` | Get transactions | Query: `?page=1&limit=20&type=donation` |
| GET | `/revenue/analytics` | Get revenue analytics | Query: `?startDate=2025-01-01&endDate=2025-12-31` |

## 📊 Analytics Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/analytics` | Get analytics dashboard | Query: `?startDate=2025-01-01&endDate=2025-06-06&metrics=views,likes,comments` |
| GET | `/analytics/content` | Get content analytics | Query: `?videoId=uuid&period=week` |
| GET | `/analytics/users` | Get user analytics | Query: `?period=month&segment=active` |
| GET | `/analytics/revenue` | Get revenue analytics | Query: `?period=quarter&year=2025` |
| GET | `/analytics/engagement` | Get engagement analytics | Query: `?contentType=video&period=week` |
| POST | `/analytics/events` | Track custom event | `{"event":"video_shared","videoId":"uuid","platform":"facebook","userId":"uuid"}` |

## 🛡️ Admin Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/admin/dashboard` | Get admin dashboard | - |
| GET | `/admin/users` | Get all users | Query: `?page=1&limit=20&status=active&role=content_producer` |
| GET | `/admin/content` | Get all content | Query: `?status=pending&page=1&limit=20` |
| GET | `/admin/reports` | Get system reports | Query: `?type=content&status=flagged` |
| POST | `/admin/bulk-actions` | Perform bulk actions | `{"action":"approve","entityType":"video","entityIds":["uuid1","uuid2"]}` |
| GET | `/admin/system-health` | Get system health | - |
| GET | `/admin/audit-logs` | Get audit logs | Query: `?page=1&limit=50&action=user_created` |
| POST | `/admin/maintenance` | System maintenance | `{"action":"clear_cache","target":"all"}` |

## 🔍 Search & Discovery Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/search/global` | Global search | Query: `?q=faith&type=all&page=1&limit=20` |
| GET | `/search/suggestions` | Get search suggestions | Query: `?q=fai` |
| GET | `/search/trending` | Get trending searches | Query: `?limit=10&period=week` |
| POST | `/search/save` | Save search | `{"query":"faith documentaries","filters":{"category":"documentary"}}` |
| GET | `/search/saved` | Get saved searches | - |

## 📱 Notifications Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/notifications` | Get user notifications | Query: `?page=1&limit=20&read=false` |
| PUT | `/notifications/:id/read` | Mark notification as read | - |
| PUT | `/notifications/read-all` | Mark all as read | - |
| DELETE | `/notifications/:id` | Delete notification | - |
| POST | `/notifications/preferences` | Update notification preferences | `{"email":true,"push":true,"sms":false}` |

## 🎯 Recommendation Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/recommendations/videos` | Get video recommendations | Query: `?limit=10&type=similar` |
| GET | `/recommendations/categories` | Get category recommendations | Query: `?limit=5` |
| GET | `/recommendations/users` | Get user recommendations | Query: `?limit=10&type=creators` |
| POST | `/recommendations/feedback` | Provide recommendation feedback | `{"recommendationId":"uuid","feedback":"liked","reason":"relevant"}` |

## 🔧 System Endpoints

| Method | Endpoint | Description | Sample Payload |
|--------|----------|-------------|----------------|
| GET | `/health` | Health check | - |
| GET | `/version` | Get API version | - |
| GET | `/config/public` | Get public configuration | - |
| GET | `/status` | Get system status | - |

## 📝 Response Format

All endpoints return JSON responses with consistent structure:

### Success Response (200/201)
```json
{
  "data": {...},
  "message": "Success message",
  "timestamp": "2025-06-06T00:00:00.000Z"
}
```

### Error Response (4xx/5xx)
```json
{
  "statusCode": 400,
  "message": "Error message",
  "error": "Bad Request",
  "details": [...],
  "timestamp": "2025-06-06T00:00:00.000Z"
}
```

### Paginated Response
```json
{
  "data": [...],
  "total": 150,
  "page": 1,
  "limit": 10,
  "totalPages": 15
}
```

## 🚀 Testing

### Using the Test Script
```bash
./test-api.sh
```

### Using Postman
1. Import `postman/FaithStream-API-Collection.json`
2. Set environment variables
3. Run the collection

### Using cURL
```bash
# Login
curl -X POST http://localhost:3090/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123!"}'

# Get categories
curl -X GET http://localhost:3090/api/v1/categories
```

## 📚 Additional Resources

- **Swagger UI:** http://localhost:3090/api/docs
- **API Documentation:** `API-Documentation.md`
- **README:** `README.md`
- **Environment Setup:** `.env.example`
