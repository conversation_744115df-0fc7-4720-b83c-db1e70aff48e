# FaithStream VOD Platform Backend

A comprehensive faith-based video-on-demand platform backend built with NestJS, featuring user management, content streaming, monetization, and analytics.

## 🚀 Features

### Core Features
- **🔐 Authentication & Authorization** - JWT-based auth with role-based access control, Google & Facebook OAuth
- **👥 User Management** - Complete user profiles, preferences, and role management
- **🎬 Content Management** - Video upload, categorization, and metadata management
- **📺 Streaming** - Video streaming with multiple quality options and progress tracking
- **💬 Social Features** - Comments, likes, shares, and user interactions
- **📊 Analytics** - Comprehensive analytics for users, content, and revenue
- **💰 Monetization** - Advertisement management and donation system
- **🎯 AI Recommendations** - Intelligent content recommendation engine
- **📱 Multi-device Support** - Responsive design for all devices

### Technical Features
- **RESTful API** - Well-structured REST endpoints with OpenAPI documentation
- **Database** - PostgreSQL with TypeORM for robust data management
- **Security** - JWT authentication, rate limiting, input validation, and CORS
- **File Upload** - Secure file upload with validation and storage management
- **Real-time Updates** - WebSocket support for live features
- **Caching** - Redis caching for improved performance
- **Logging** - Comprehensive logging and error tracking

## 🛠️ Technology Stack

- **Framework:** NestJS (Node.js)
- **Language:** TypeScript
- **Database:** PostgreSQL
- **ORM:** TypeORM
- **Authentication:** JWT with Passport, Google OAuth, Facebook OAuth
- **Validation:** class-validator
- **Documentation:** Swagger/OpenAPI
- **File Upload:** Multer
- **Security:** Helmet, CORS protection
- **Testing:** Jest
- **Package Manager:** npm

## 📋 Prerequisites

- Node.js (v18 or higher)
- PostgreSQL (v13 or higher)
- npm or yarn

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd faithstream-backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
Copy the environment file and configure your settings:
```bash
cp .env.example .env
```

Update the `.env` file with your configuration:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=faithstream_user
DB_PASSWORD=your_password
DB_DATABASE=faithstream_db

# Application Configuration
PORT=3090
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Security
BCRYPT_ROUNDS=12

# File Upload
MAX_FILE_SIZE=100MB
UPLOAD_PATH=./uploads

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 4. Database Setup
Create the PostgreSQL database:
```sql
CREATE DATABASE faithstream_db;
CREATE USER faithstream_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE faithstream_db TO faithstream_user;
```

### 5. Run Database Migrations
```bash
npm run migration:run
```

### 6. Seed Initial Data
```bash
npm run seed
```

### 7. Start the Application
```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

The API will be available at `http://localhost:3090`

## 📚 API Documentation

### Interactive Documentation
Visit `http://localhost:3090/api/docs` for the interactive Swagger UI documentation.

### Postman Collection
Import the `postman/FaithStream-API-Collection.json` file into Postman for easy API testing.

### API Reference
See `API-Documentation.md` for detailed endpoint documentation with examples.

## 🔑 Default Credentials

After seeding the database, you can use these default credentials:

**Super Admin:**
- Email: `<EMAIL>`
- Password: `Admin123!`

**Content Producer:**
- Email: `<EMAIL>`
- Password: `Producer123!`

**Standard User:**
- Email: `<EMAIL>`
- Password: `User123!`

## 🏗️ Project Structure

```
faithstream-backend/
├── src/
│   ├── auth/                 # Authentication module
│   ├── users/                # User management
│   ├── content/              # Content management
│   ├── categories/           # Category management
│   ├── tags/                 # Tag management
│   ├── streaming/            # Streaming features
│   ├── playlists/            # Playlist management
│   ├── ads/                  # Advertisement management
│   ├── revenue/              # Revenue and donations
│   ├── analytics/            # Analytics and reporting
│   ├── admin/                # Admin features
│   ├── entities/             # Database entities
│   ├── common/               # Shared utilities
│   ├── config/               # Configuration
│   └── main.ts               # Application entry point
├── uploads/                  # File uploads directory
├── postman/                  # Postman collection
├── .env                      # Environment variables
├── package.json              # Dependencies
└── README.md                 # This file
```

## 🧪 Testing

### Run Tests
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

### Test Database
For testing, create a separate test database:
```sql
CREATE DATABASE faithstream_test_db;
```

Update your test environment configuration accordingly.

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Docker Deployment
```bash
# Build Docker image
docker build -t faithstream-backend .

# Run with Docker Compose
docker-compose up -d
```

### Environment Variables for Production
Ensure these environment variables are set in production:
- `NODE_ENV=production`
- `JWT_SECRET` (use a strong, unique secret)
- Database credentials
- SMTP configuration for email features

## 📊 Database Schema

The application uses the following main entities:
- **Users** - User accounts and profiles
- **Roles** - Role-based access control
- **Videos** - Video content and metadata
- **Categories** - Content categorization
- **Tags** - Content tagging system
- **Comments** - User comments and replies
- **Likes** - User likes and reactions
- **WatchHistory** - User viewing history
- **Playlists** - User-created playlists
- **Advertisements** - Ad management
- **Donations** - Donation tracking
- **Analytics** - Usage analytics

## 🔧 Configuration

### Database Configuration
The application supports PostgreSQL with TypeORM. Configure your database connection in the `.env` file.

### File Upload Configuration
Configure file upload settings:
- `MAX_FILE_SIZE` - Maximum file size for uploads
- `UPLOAD_PATH` - Directory for storing uploaded files
- Supported formats: MP4, AVI, MOV for videos; JPG, PNG for images

### Security Configuration
- JWT token expiration
- Password hashing rounds
- Rate limiting settings
- CORS configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write unit tests for new features
- Update documentation for API changes
- Use conventional commit messages
- Ensure code passes linting and tests

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- **Documentation:** `http://localhost:3090/api/docs`
- **Email:** <EMAIL>
- **Issues:** Create an issue in the repository

## 🙏 Acknowledgments

- Built with [NestJS](https://nestjs.com/)
- Database powered by [PostgreSQL](https://postgresql.org/)
- Authentication with [Passport](http://passportjs.org/)
- API documentation with [Swagger](https://swagger.io/)

---

**FaithStream VOD Platform** - Spreading faith through technology 🙏
