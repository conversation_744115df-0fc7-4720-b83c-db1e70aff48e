#!/bin/bash

# =================================
# FaithStream VOD Platform API Test Script
# =================================


# Configuration
BASE_URL="http://localhost:3090/api/v1"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="Admin123!"
TEST_USER_EMAIL="<EMAIL>"
TEST_USER_PASSWORD="TestUser123!"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Global variables
# Global variables
ACCESS_TOKEN=""
USER_ID=""
VIDEO_ID=""
CATEGORY_ID=""

# Helper functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Test function
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    local auth_header=""
    
    if [ ! -z "$ACCESS_TOKEN" ]; then
        auth_header="-H \"Authorization: Bearer $ACCESS_TOKEN\""
    fi
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(eval curl -s -w "HTTPSTATUS:%{http_code}" $auth_header "$BASE_URL$endpoint")
    else
        response=$(eval curl -s -w "HTTPSTATUS:%{http_code}" -X $method $auth_header -H "Content-Type: application/json" -d "'$data'" "$BASE_URL$endpoint")
    fi
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ $http_code -ge 200 ] && [ $http_code -lt 300 ]; then
        print_success "HTTP $http_code - $description"
        echo "$body" | jq . 2>/dev/null || echo "$body"
        return 0
    else
        print_error "HTTP $http_code - $description"
        echo "$body"
        return 1
    fi
}

# Start testing
print_header "FaithStream VOD Platform API Testing"
print_info "Base URL: $BASE_URL"

# Test 1: Health Check
print_header "Health Check"
test_endpoint "GET" "/health" "" "Health check endpoint"

# Test 2: Get Categories (Public endpoint)
print_header "Public Endpoints"
if test_endpoint "GET" "/categories" "" "Get all categories"; then
    CATEGORY_ID=$(echo "$body" | jq -r '.[0].id' 2>/dev/null)
    print_info "Category ID: $CATEGORY_ID"
fi

# Test 3: Get Tags
test_endpoint "GET" "/tags" "" "Get all tags"

# Test 4: Search Videos (Public)
test_endpoint "GET" "/content/search?q=faith" "" "Search videos"

# Test 5: Admin Login
print_header "Authentication"
login_data="{\"email\":\"$ADMIN_EMAIL\",\"password\":\"$ADMIN_PASSWORD\"}"
if test_endpoint "POST" "/auth/login" "$login_data" "Admin login"; then
    ACCESS_TOKEN=$(echo "$body" | jq -r '.accessToken' 2>/dev/null)
    USER_ID=$(echo "$body" | jq -r '.user.id' 2>/dev/null)
    print_success "Access token obtained"
    print_info "User ID: $USER_ID"
fi

# Test 6: Get User Profile
print_header "User Management"
test_endpoint "GET" "/auth/profile" "" "Get current user profile"

# Test 7: Get User Stats
test_endpoint "GET" "/users/me/stats" "" "Get user statistics"

# Test 8: Get All Users (Admin)
test_endpoint "GET" "/users?page=1&limit=5" "" "Get all users (admin)"

# Test 9: Content Management
print_header "Content Management"
test_endpoint "GET" "/content" "" "Get all videos"

# Test 10: Get Featured Videos
test_endpoint "GET" "/content/featured?limit=5" "" "Get featured videos"

# Test 11: Get Trending Videos
test_endpoint "GET" "/content/trending?limit=5" "" "Get trending videos"

# Test 12: Get Recommended Videos
test_endpoint "GET" "/content/recommended?limit=5" "" "Get recommended videos"

# Test 13: Create New Video
print_header "Content Creation"
video_data='{
  "title": "Test Faith Video",
  "description": "A test video for API testing",
  "videoUrl": "https://example.com/test-video.mp4",
  "thumbnail": "https://example.com/test-thumbnail.jpg",
  "duration": 3600,
  "type": "documentary",
  "ageRating": "PG",
  "language": "en",
  "allowComments": true,
  "allowLikes": true,
  "allowSharing": true,
  "keywords": ["test", "faith", "api"]
}'

if test_endpoint "POST" "/content" "$video_data" "Create new video"; then
    VIDEO_ID=$(echo "$body" | jq -r '.id' 2>/dev/null)
    print_info "Video ID: $VIDEO_ID"
fi

# Test 14: Get Video by ID
if [ ! -z "$VIDEO_ID" ]; then
    test_endpoint "GET" "/content/$VIDEO_ID" "" "Get video by ID"
fi

# Test 15: Streaming Features
print_header "Streaming Features"
if [ ! -z "$VIDEO_ID" ]; then
    # Test video streams
    test_endpoint "GET" "/streaming/videos/$VIDEO_ID/streams" "" "Get video streams"
    
    # Test watch progress update
    progress_data='{
      "watchTimeSeconds": 900,
      "progressPercentage": 25,
      "lastPositionSeconds": 900,
      "completed": false,
      "quality": "720p",
      "deviceType": "desktop"
    }'
    test_endpoint "PUT" "/streaming/videos/$VIDEO_ID/progress" "$progress_data" "Update watch progress"
    
    # Test like video
    test_endpoint "POST" "/streaming/videos/$VIDEO_ID/like" "" "Like video"
    
    # Test comment on video
    comment_data='{"content": "Great video! Very inspiring.", "parentId": null}'
    test_endpoint "POST" "/streaming/videos/$VIDEO_ID/comments" "$comment_data" "Post comment"
    
    # Test get comments
    test_endpoint "GET" "/streaming/videos/$VIDEO_ID/comments?page=1&limit=10" "" "Get video comments"
fi

# Test 16: Watch History
test_endpoint "GET" "/streaming/watch-history?page=1&limit=10" "" "Get watch history"

# Test 17: Continue Watching
test_endpoint "GET" "/streaming/continue-watching?limit=5" "" "Get continue watching"

# Test 18: Playlists
print_header "Playlists"
playlist_data='{
  "name": "Test Playlist",
  "description": "A test playlist for API testing",
  "isPublic": true
}'
test_endpoint "POST" "/playlists" "$playlist_data" "Create playlist"

# Test 19: Get User Playlists
test_endpoint "GET" "/playlists" "" "Get user playlists"

# Test 20: Categories Management
print_header "Categories Management"
category_data='{
  "name": "Test Category",
  "description": "A test category for API testing",
  "type": "genre",
  "icon": "fa-test",
  "color": "#FF5733"
}'
test_endpoint "POST" "/categories" "$category_data" "Create category"

# Test 21: Tags Management
print_header "Tags Management"
tag_data='{
  "name": "test-tag",
  "description": "A test tag for API testing"
}'
test_endpoint "POST" "/tags" "$tag_data" "Create tag"

# Test 22: Analytics
print_header "Analytics"
test_endpoint "GET" "/analytics?startDate=2025-01-01&endDate=2025-12-31" "" "Get analytics dashboard"

# Test 23: Admin Dashboard
print_header "Admin Features"
test_endpoint "GET" "/admin/dashboard" "" "Get admin dashboard"

# Test 24: Revenue Overview
print_header "Revenue & Monetization"
test_endpoint "GET" "/revenue?period=month&year=2025&month=6" "" "Get revenue overview"

# Test 25: User Registration (New User)
print_header "User Registration"
register_data='{
  "email": "<EMAIL>",
  "password": "NewTestUser123!",
  "firstName": "New",
  "lastName": "TestUser",
  "username": "newtestuser"
}'
test_endpoint "POST" "/auth/register" "$register_data" "Register new user"

# Test 26: Forgot Password
forgot_password_data='{"email": "<EMAIL>"}'
test_endpoint "POST" "/auth/forgot-password" "$forgot_password_data" "Forgot password request"

# Test 27: Get Category Hierarchy
print_header "Advanced Features"
test_endpoint "GET" "/categories/hierarchy" "" "Get category hierarchy"

# Test 28: Get Popular Tags
test_endpoint "GET" "/tags/popular?limit=10" "" "Get popular tags"

# Test 29: Get Tag Cloud
test_endpoint "GET" "/tags/cloud?limit=20" "" "Get tag cloud"

# Test 30: Search with Filters
test_endpoint "GET" "/content/search?q=faith&category=faith-based&language=en&sort=relevance" "" "Advanced search with filters"

# Summary
print_header "Test Summary"
print_info "API testing completed!"
print_info "Base URL: $BASE_URL"
print_info "Admin credentials used: $ADMIN_EMAIL"

if [ ! -z "$ACCESS_TOKEN" ]; then
    print_success "Authentication successful"
else
    print_error "Authentication failed"
fi

if [ ! -z "$VIDEO_ID" ]; then
    print_success "Content creation successful"
else
    print_error "Content creation failed"
fi

print_info "Check the output above for detailed test results"
print_info "For interactive testing, use the Swagger UI at: $BASE_URL/../docs"

echo -e "\n${GREEN}Testing completed!${NC}"
